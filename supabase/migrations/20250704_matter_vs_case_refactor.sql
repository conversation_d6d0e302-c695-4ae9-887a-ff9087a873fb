-- Migration: Matter vs. Case Refactor
-- Date: 2025-07-04
-- Description: Replace the single "case" concept with a generic "matter" entity 
-- while preserving the "case" label for litigation contexts.
-- This migration ensures backward compatibility for existing APIs and data.

BEGIN;

-- Step 1: Add practice_area column to existing cases table
-- Default to 'litigation' to maintain current behavior
ALTER TABLE tenants.cases 
ADD COLUMN IF NOT EXISTS practice_area TEXT NOT NULL DEFAULT 'litigation'
CHECK (practice_area IN ('litigation', 'transaction', 'advisory', 'ip'));

-- Step 2: Add computed column for display label
-- This will show 'Case' for litigation, 'Matter' for everything else
ALTER TABLE tenants.cases 
ADD COLUMN IF NOT EXISTS display_label TEXT 
GENERATED ALWAYS AS (
  CASE 
    WHEN practice_area = 'litigation' THEN 'Case'
    ELSE 'Matter'
  END
) STORED;

-- Step 3: Create the new matters table by renaming cases
-- We'll do this in a way that preserves all data and relationships
ALTER TABLE tenants.cases RENAME TO matters;

-- Step 4: Update all foreign key references to point to matters table
-- Update assignments table
ALTER TABLE tenants.assignments 
DROP CONSTRAINT IF EXISTS assignments_case_id_fkey,
ADD CONSTRAINT assignments_matter_id_fkey 
FOREIGN KEY (case_id) REFERENCES tenants.matters(id) ON DELETE CASCADE;

-- Rename the case_id column to matter_id in assignments for clarity
-- But keep case_id as an alias for backward compatibility
ALTER TABLE tenants.assignments 
ADD COLUMN IF NOT EXISTS matter_id UUID;

-- Copy data from case_id to matter_id
UPDATE tenants.assignments SET matter_id = case_id WHERE matter_id IS NULL;

-- Add foreign key constraint for matter_id
ALTER TABLE tenants.assignments 
ADD CONSTRAINT assignments_matter_id_fkey2 
FOREIGN KEY (matter_id) REFERENCES tenants.matters(id) ON DELETE CASCADE;

-- Update case_documents table if it exists
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables 
             WHERE table_schema = 'tenants' AND table_name = 'case_documents') THEN
    
    -- Add matter_id column
    ALTER TABLE tenants.case_documents 
    ADD COLUMN IF NOT EXISTS matter_id UUID;
    
    -- Copy data from case_id to matter_id
    UPDATE tenants.case_documents SET matter_id = case_id WHERE matter_id IS NULL;
    
    -- Add foreign key constraint
    ALTER TABLE tenants.case_documents 
    ADD CONSTRAINT case_documents_matter_id_fkey 
    FOREIGN KEY (matter_id) REFERENCES tenants.matters(id) ON DELETE CASCADE;
    
  END IF;
END $$;

-- Update tasks table if it has case references
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns 
             WHERE table_schema = 'tenants' AND table_name = 'tasks' AND column_name = 'related_case') THEN
    
    -- Rename related_case to related_matter
    ALTER TABLE tenants.tasks 
    ADD COLUMN IF NOT EXISTS related_matter UUID;
    
    -- Copy data
    UPDATE tenants.tasks SET related_matter = related_case WHERE related_matter IS NULL;
    
    -- Add foreign key constraint
    ALTER TABLE tenants.tasks 
    ADD CONSTRAINT tasks_related_matter_fkey 
    FOREIGN KEY (related_matter) REFERENCES tenants.matters(id) ON DELETE SET NULL;
    
  END IF;
END $$;

-- Step 5: Create backward compatibility view
-- This allows existing code to continue working with "cases" table name
CREATE OR REPLACE VIEW tenants.cases AS 
SELECT 
  id,
  tenant_id,
  title,
  description,
  status,
  sensitive,
  created_by,
  created_at,
  updated_at,
  metadata,
  rejection_reason,
  client_id,
  practice_area,
  display_label,
  -- Add computed fields for backward compatibility
  id as case_id,
  practice_area as case_type
FROM tenants.matters;

-- Step 6: Create INSTEAD OF triggers for the view to handle INSERT/UPDATE/DELETE
-- This ensures the compatibility view is fully functional

-- INSERT trigger
CREATE OR REPLACE FUNCTION tenants.cases_insert_trigger()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO tenants.matters (
    id, tenant_id, title, description, status, sensitive, 
    created_by, created_at, updated_at, metadata, 
    rejection_reason, client_id, practice_area
  ) VALUES (
    COALESCE(NEW.id, gen_random_uuid()),
    NEW.tenant_id,
    NEW.title,
    NEW.description,
    NEW.status,
    NEW.sensitive,
    NEW.created_by,
    COALESCE(NEW.created_at, NOW()),
    COALESCE(NEW.updated_at, NOW()),
    NEW.metadata,
    NEW.rejection_reason,
    NEW.client_id,
    COALESCE(NEW.practice_area, 'litigation')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cases_insert_trigger
  INSTEAD OF INSERT ON tenants.cases
  FOR EACH ROW EXECUTE FUNCTION tenants.cases_insert_trigger();

-- UPDATE trigger
CREATE OR REPLACE FUNCTION tenants.cases_update_trigger()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE tenants.matters SET
    title = NEW.title,
    description = NEW.description,
    status = NEW.status,
    sensitive = NEW.sensitive,
    updated_at = COALESCE(NEW.updated_at, NOW()),
    metadata = NEW.metadata,
    rejection_reason = NEW.rejection_reason,
    client_id = NEW.client_id,
    practice_area = COALESCE(NEW.practice_area, OLD.practice_area)
  WHERE id = OLD.id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cases_update_trigger
  INSTEAD OF UPDATE ON tenants.cases
  FOR EACH ROW EXECUTE FUNCTION tenants.cases_update_trigger();

-- DELETE trigger
CREATE OR REPLACE FUNCTION tenants.cases_delete_trigger()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM tenants.matters WHERE id = OLD.id;
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cases_delete_trigger
  INSTEAD OF DELETE ON tenants.cases
  FOR EACH ROW EXECUTE FUNCTION tenants.cases_delete_trigger();

-- Step 7: Update RLS policies to work with matters table
-- Drop existing policies on cases (they're now on the view)
DROP POLICY IF EXISTS tenant_isolation ON tenants.cases;

-- Create RLS policies for matters table
ALTER TABLE tenants.matters ENABLE ROW LEVEL SECURITY;

-- Recreate tenant isolation policy for matters
CREATE POLICY tenant_isolation ON tenants.matters
  FOR ALL
  USING (tenant_id = current_setting('app.tenant_id')::UUID);

-- Create policies for different user roles if they exist
DO $$
BEGIN
  -- Check if partner role exists and create policy
  IF EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'partner') THEN
    CREATE POLICY partner_matters_all ON tenants.matters
      FOR ALL
      TO partner
      USING (tenant_id = current_setting('app.tenant_id')::uuid);
  END IF;
  
  -- Add other role policies as needed
  -- This can be extended based on existing role structure
END $$;

-- Step 8: Create indexes for performance
-- Copy existing indexes from cases to matters (if they don't exist)
CREATE INDEX IF NOT EXISTS idx_matters_tenant_id ON tenants.matters(tenant_id);
CREATE INDEX IF NOT EXISTS idx_matters_client_id ON tenants.matters(client_id);
CREATE INDEX IF NOT EXISTS idx_matters_status ON tenants.matters(status);
CREATE INDEX IF NOT EXISTS idx_matters_practice_area ON tenants.matters(practice_area);
CREATE INDEX IF NOT EXISTS idx_matters_created_at ON tenants.matters(created_at);

-- Step 9: Add comments for documentation
COMMENT ON TABLE tenants.matters IS 'Legal matters (formerly cases). Supports different practice areas with dynamic labeling.';
COMMENT ON COLUMN tenants.matters.practice_area IS 'Type of legal practice: litigation, transaction, advisory, or ip';
COMMENT ON COLUMN tenants.matters.display_label IS 'Computed label: Case for litigation, Matter for others';
COMMENT ON VIEW tenants.cases IS 'Backward compatibility view for existing case-based code';

-- Step 10: Grant appropriate permissions
-- Grant permissions to authenticated users (adjust based on your auth setup)
GRANT SELECT, INSERT, UPDATE, DELETE ON tenants.matters TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON tenants.cases TO authenticated;

COMMIT;
