"""
Tests for the Matter and Client CRUD Agent.

This module contains tests for the Matter and Client CRUD Agent implementation.
"""

import uuid
from unittest.mock import AsyncMock, patch

import pytest
from langchain_core.messages import AIMessage, HumanMessage

from backend.agents.matter_client.agent import MatterClientAgent
from backend.agents.matter_client.nodes import (
    create_matter,
    create_client,
    delete_matter,
    delete_client,
    read_matter,
    read_client,
    update_matter,
    update_client,
)
from backend.agents.matter_client.router import matter_client_router


@pytest.fixture
def mock_resolve_llm():
    """Mock the resolve_llm function."""
    with patch("backend.agents.matter_client.nodes.resolve_llm") as mock_resolve:
        mock_llm_instance = AsyncMock()
        mock_llm_instance.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "{}"
                    }
                }
            ]
        }
        # Make resolve_llm an async function that returns the mock LLM instance
        async def async_resolve_llm(*args, **kwargs):
            return mock_llm_instance

        mock_resolve.side_effect = async_resolve_llm
        yield mock_llm_instance


@pytest.fixture
def mock_db_functions():
    """Mock the database functions."""
    with patch("backend.agents.matter_client.nodes.create_matter_db") as mock_create_matter, \
         patch("backend.agents.matter_client.nodes.get_matter_by_id") as mock_get_matter, \
         patch("backend.agents.matter_client.nodes.list_matters_db") as mock_list_matters, \
         patch("backend.agents.matter_client.nodes.update_matter_db") as mock_update_matter, \
         patch("backend.agents.matter_client.nodes.delete_matter_db") as mock_delete_matter, \
         patch("backend.agents.matter_client.nodes.create_client_db") as mock_create_client, \
         patch("backend.agents.matter_client.nodes.get_client_by_id") as mock_get_client, \
         patch("backend.agents.matter_client.nodes.list_clients_db") as mock_list_clients, \
         patch("backend.agents.matter_client.nodes.update_client_db") as mock_update_client, \
         patch("backend.agents.matter_client.nodes.delete_client_db") as mock_delete_client:

        # Set up return values
        mock_create_matter.return_value = {
            "id": str(uuid.uuid4()),
            "tenant_id": str(uuid.uuid4()),
            "title": "Test Matter",
            "description": "Test Description",
            "status": "active",
            "practice_area": "litigation",
            "sensitive": False,
            "client_id": str(uuid.uuid4()),
            "created_by": str(uuid.uuid4()),
            "created_at": "2023-01-01T00:00:00Z",
            "metadata": {},
        }

        mock_get_matter.return_value = {
            "id": str(uuid.uuid4()),
            "tenant_id": str(uuid.uuid4()),
            "title": "Test Matter",
            "description": "Test Description",
            "status": "active",
            "practice_area": "litigation",
            "sensitive": False,
            "client_id": str(uuid.uuid4()),
            "created_by": str(uuid.uuid4()),
            "created_at": "2023-01-01T00:00:00Z",
            "metadata": {},
        }

        mock_list_matters.return_value = [
            {
                "id": str(uuid.uuid4()),
                "tenant_id": str(uuid.uuid4()),
                "title": "Test Matter 1",
                "description": "Test Description 1",
                "status": "active",
                "practice_area": "litigation",
                "sensitive": False,
                "client_id": str(uuid.uuid4()),
                "created_by": str(uuid.uuid4()),
                "created_at": "2023-01-01T00:00:00Z",
                "metadata": {},
            },
            {
                "id": str(uuid.uuid4()),
                "tenant_id": str(uuid.uuid4()),
                "title": "Test Matter 2",
                "description": "Test Description 2",
                "status": "active",
                "practice_area": "corporate",
                "sensitive": True,
                "client_id": str(uuid.uuid4()),
                "created_by": str(uuid.uuid4()),
                "created_at": "2023-01-02T00:00:00Z",
                "metadata": {},
            },
        ]

        mock_update_matter.return_value = {
            "id": str(uuid.uuid4()),
            "tenant_id": str(uuid.uuid4()),
            "title": "Updated Test Matter",
            "description": "Updated Test Description",
            "status": "active",
            "practice_area": "litigation",
            "sensitive": False,
            "client_id": str(uuid.uuid4()),
            "created_by": str(uuid.uuid4()),
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-03T00:00:00Z",
            "metadata": {},
        }

        mock_delete_matter.return_value = True

        mock_create_client.return_value = {
            "id": str(uuid.uuid4()),
            "tenant_id": str(uuid.uuid4()),
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone_primary": "************",
            "created_by": str(uuid.uuid4()),
            "created_at": "2023-01-01T00:00:00Z",
            "metadata": {},
        }

        mock_get_client.return_value = {
            "id": str(uuid.uuid4()),
            "tenant_id": str(uuid.uuid4()),
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone_primary": "************",
            "created_by": str(uuid.uuid4()),
            "created_at": "2023-01-01T00:00:00Z",
            "metadata": {},
        }

        mock_list_clients.return_value = [
            {
                "id": str(uuid.uuid4()),
                "tenant_id": str(uuid.uuid4()),
                "first_name": "John",
                "last_name": "Doe",
                "email": "<EMAIL>",
                "phone_primary": "************",
                "created_by": str(uuid.uuid4()),
                "created_at": "2023-01-01T00:00:00Z",
                "metadata": {},
            },
            {
                "id": str(uuid.uuid4()),
                "tenant_id": str(uuid.uuid4()),
                "first_name": "Jane",
                "last_name": "Smith",
                "email": "<EMAIL>",
                "phone_primary": "************",
                "created_by": str(uuid.uuid4()),
                "created_at": "2023-01-02T00:00:00Z",
                "metadata": {},
            },
        ]

        mock_update_client.return_value = {
            "id": str(uuid.uuid4()),
            "tenant_id": str(uuid.uuid4()),
            "first_name": "Updated John",
            "last_name": "Updated Doe",
            "email": "<EMAIL>",
            "phone_primary": "************",
            "created_by": str(uuid.uuid4()),
            "created_at": "2023-01-01T00:00:00Z",
            "updated_at": "2023-01-03T00:00:00Z",
            "metadata": {},
        }

        mock_delete_client.return_value = True

        yield {
            "create_matter": mock_create_matter,
            "get_matter": mock_get_matter,
            "list_matters": mock_list_matters,
            "update_matter": mock_update_matter,
            "delete_matter": mock_delete_matter,
            "create_client": mock_create_client,
            "get_client": mock_get_client,
            "list_clients": mock_list_clients,
            "update_client": mock_update_client,
            "delete_client": mock_delete_client,
        }


@pytest.mark.asyncio
async def test_matter_client_router():
    """Test the matter_client_router function."""
    with patch("backend.agents.matter_client.router.resolve_llm") as mock_resolve:
        mock_llm_instance = AsyncMock()
        mock_llm_instance.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "create_matter"
                    }
                }
            ]
        }

        # Make resolve_llm an async function that returns the mock LLM instance
        async def async_resolve_llm(*args, **kwargs):
            return mock_llm_instance

        mock_resolve.side_effect = async_resolve_llm

        # Set up the state
        state = {
            "messages": [
                HumanMessage(content="Create a new case for John Doe")
            ]
        }

        # Call the router
        result = await matter_client_router(state, {})

        # Verify the result - the router should route to create_matter for this input
        assert result == {"next": "create_matter"}


@pytest.mark.asyncio
async def test_create_matter(mock_resolve_llm, mock_db_functions):
    """Test the create_matter function."""
    # Set up the mock response
    mock_resolve_llm.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": """
                    {
                        "title": "Test Case",
                        "description": "Test Description",
                        "status": "active",
                        "case_type": "personal_injury",
                        "sensitive": false,
                        "client_id": null
                    }
                    """
                }
            }
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Create a new case for John Doe")
        ],
        "tenant_id": str(uuid.uuid4()),
        "user_id": str(uuid.uuid4())
    }

    # Call the function
    result = await create_matter(state, {})

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][1], AIMessage)
    assert "Matter created successfully" in result["messages"][1].content

    # Verify the database function was called
    mock_db_functions["create_matter"].assert_called_once()


@pytest.mark.asyncio
async def test_read_matter(mock_resolve_llm, mock_db_functions):
    """Test the read_matter function."""
    # Set up the mock response
    mock_resolve_llm.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": """
                    {
                        "case_id": "123e4567-e89b-12d3-a456-************"
                    }
                    """
                }
            }
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Get case 123e4567-e89b-12d3-a456-************")
        ],
        "tenant_id": str(uuid.uuid4())
    }

    # Call the function
    result = await read_matter(state, {})

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][1], AIMessage)
    assert "Case found" in result["messages"][1].content

    # Verify the database function was called
    mock_db_functions["get_case"].assert_called_once()


@pytest.mark.asyncio
async def test_update_matter(mock_resolve_llm, mock_db_functions):
    """Test the update_matter function."""
    # Set up the mock response
    mock_resolve_llm.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": """
                    {
                        "case_id": "123e4567-e89b-12d3-a456-************",
                        "title": "Updated Test Case",
                        "description": "Updated Test Description"
                    }
                    """
                }
            }
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Update case 123e4567-e89b-12d3-a456-************ with title 'Updated Test Case'")
        ],
        "tenant_id": str(uuid.uuid4()),
        "user_id": str(uuid.uuid4())
    }

    # Call the function
    result = await update_matter(state, {})

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][1], AIMessage)
    assert "Case updated successfully" in result["messages"][1].content

    # Verify the database function was called
    mock_db_functions["update_matter"].assert_called_once()


@pytest.mark.asyncio
async def test_delete_matter(mock_resolve_llm, mock_db_functions):
    """Test the delete_matter function."""
    # Set up the mock response
    mock_resolve_llm.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": """
                    {
                        "case_id": "123e4567-e89b-12d3-a456-************"
                    }
                    """
                }
            }
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Delete case 123e4567-e89b-12d3-a456-************")
        ],
        "tenant_id": str(uuid.uuid4())
    }

    # Call the function
    result = await delete_matter(state, {})

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][1], AIMessage)
    assert "Case deleted successfully" in result["messages"][1].content

    # Verify the database function was called
    mock_db_functions["delete_matter"].assert_called_once()


@pytest.mark.asyncio
async def test_create_client(mock_resolve_llm, mock_db_functions):
    """Test the create_client function."""
    # Set up the mock response
    mock_resolve_llm.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": """
                    {
                        "first_name": "John",
                        "last_name": "Doe",
                        "email": "<EMAIL>",
                        "phone_primary": "************"
                    }
                    """
                }
            }
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Create a new client named John Doe")
        ],
        "tenant_id": str(uuid.uuid4()),
        "user_id": str(uuid.uuid4())
    }

    # Call the function
    result = await create_client(state, {})

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][1], AIMessage)
    assert "Client created successfully" in result["messages"][1].content

    # Verify the database function was called
    mock_db_functions["create_client"].assert_called_once()


@pytest.mark.asyncio
async def test_read_client(mock_resolve_llm, mock_db_functions):
    """Test the read_client function."""
    # Set up the mock response
    mock_resolve_llm.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": """
                    {
                        "client_id": "123e4567-e89b-12d3-a456-************"
                    }
                    """
                }
            }
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Get client 123e4567-e89b-12d3-a456-************")
        ],
        "tenant_id": str(uuid.uuid4())
    }

    # Call the function
    result = await read_client(state, {})

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][1], AIMessage)
    assert "Client found" in result["messages"][1].content

    # Verify the database function was called
    mock_db_functions["get_client"].assert_called_once()


@pytest.mark.asyncio
async def test_update_client(mock_resolve_llm, mock_db_functions):
    """Test the update_client function."""
    # Set up the mock response
    mock_resolve_llm.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": """
                    {
                        "client_id": "123e4567-e89b-12d3-a456-************",
                        "first_name": "Updated John",
                        "last_name": "Updated Doe",
                        "email": "<EMAIL>"
                    }
                    """
                }
            }
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Update client 123e4567-e89b-12d3-a456-************ with name 'Updated John Updated Doe'")
        ],
        "tenant_id": str(uuid.uuid4()),
        "user_id": str(uuid.uuid4())
    }

    # Call the function
    result = await update_client(state, {})

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][1], AIMessage)
    assert "Client updated successfully" in result["messages"][1].content

    # Verify the database function was called
    mock_db_functions["update_client"].assert_called_once()


@pytest.mark.asyncio
async def test_delete_client(mock_resolve_llm, mock_db_functions):
    """Test the delete_client function."""
    # Set up the mock response
    mock_resolve_llm.chat_completion.return_value = {
        "choices": [
            {
                "message": {
                    "content": """
                    {
                        "client_id": "123e4567-e89b-12d3-a456-************"
                    }
                    """
                }
            }
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Delete client 123e4567-e89b-12d3-a456-************")
        ],
        "tenant_id": str(uuid.uuid4())
    }

    # Call the function
    result = await delete_client(state, {})

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][1], AIMessage)
    assert "Client deleted successfully" in result["messages"][1].content

    # Verify the database function was called
    mock_db_functions["delete_client"].assert_called_once()


@pytest.mark.asyncio
async def test_unauthorized_access():
    """Test unauthorized access (missing tenant_id)."""
    # Set up the state without tenant_id
    state = {
        "messages": [
            HumanMessage(content="Create a new case for John Doe")
        ]
    }

    # Call the create_matter function
    matter_result = await create_matter(state.copy(), {})

    # Verify the matter_result
    assert len(matter_result["messages"]) == 2
    assert isinstance(matter_result["messages"][1], AIMessage)
    assert "Error: Tenant ID is required" in matter_result["messages"][1].content

    # Call the create_client function with a fresh state
    client_result = await create_client(state.copy(), {})

    # Verify the client_result
    assert len(client_result["messages"]) == 2
    assert isinstance(client_result["messages"][1], AIMessage)
    assert "Error: Tenant ID is required" in client_result["messages"][1].content


@pytest.mark.asyncio
async def test_agent_invoke():
    """Test the CaseClientAgent invoke method."""
    # Create a mock agent
    agent = CaseClientAgent()

    # Mock the graph
    agent.graph = AsyncMock()
    agent.graph.ainvoke.return_value = {
        "messages": [
            HumanMessage(content="Create a new case"),
            AIMessage(content="Case created successfully!")
        ]
    }

    # Set up the state
    state = {
        "messages": [
            HumanMessage(content="Create a new case")
        ]
    }

    # Set up the config
    config = {
        "configurable": {
            "tenant_id": str(uuid.uuid4()),
            "user_id": str(uuid.uuid4()),
            "thread_id": str(uuid.uuid4())
        }
    }

    # Call the agent
    result = await agent.invoke(state, config)

    # Verify the result
    assert len(result["messages"]) == 2
    assert isinstance(result["messages"][0], HumanMessage)
    assert isinstance(result["messages"][1], AIMessage)
    assert "Case created successfully" in result["messages"][1].content

    # Verify the graph was called
    agent.graph.ainvoke.assert_called_once()
