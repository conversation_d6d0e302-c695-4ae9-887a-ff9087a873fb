"""
Database utilities for the Matter and Client CRUD Agent.

This module provides functions for interacting with the database for matter and
client operations.
"""

import logging
from datetime import date, datetime, timezone
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from backend.db.session import get_db
from backend.models import Matter, Client

# Set up logging
logger = logging.getLogger(__name__)

# Matter database functions
async def create_matter_db(
    tenant_id: UUID,
    title: str,
    description: Optional[str] = None,
    status: str = "active",
    practice_area: Optional[str] = None,
    sensitive: bool = False,
    client_id: Optional[UUID] = None,
    created_by: Optional[UUID] = None,
    matter_metadata: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    Create a new matter in the database.

    Args:
        tenant_id: Tenant ID
        title: Matter title
        description: Matter description
        status: Matter status
        practice_area: Practice area (determines display label)
        sensitive: Whether the matter contains sensitive information
        client_id: Client ID
        created_by: User ID of the creator
        metadata: Additional metadata

    Returns:
        The created matter
    """
    logger.info(f"Creating matter: {title}")

    async with get_db() as session:
        # Create the matter
        matter = Matter(
            tenant_id=tenant_id,
            title=title,
            description=description,
            status=status,
            practice_area=practice_area or "litigation",
            sensitive=sensitive,
            client_id=client_id,
            created_by=created_by,
            created_at=datetime.now(timezone.utc),
            metadata=matter_metadata or {},
        )

        # Add the matter to the session
        session.add(matter)

        # Commit the transaction
        await session.commit()

        # Refresh the matter to get the generated ID
        await session.refresh(matter)

        # Convert to dictionary
        return {
            "id": str(matter.id),
            "tenant_id": str(matter.tenant_id),
            "title": matter.title,
            "description": matter.description,
            "status": matter.status,
            "practice_area": matter.practice_area,
            "sensitive": matter.sensitive,
            "client_id": str(matter.client_id) if matter.client_id else None,
            "created_by": str(matter.created_by) if matter.created_by else None,
            "created_at": matter.created_at.isoformat(),
            "metadata": matter.matter_metadata,
        }

async def get_matter_db(
    session: AsyncSession,
    matter_id: UUID,
    tenant_id: UUID,
) -> Optional[Matter]:
    """
    Get a matter by ID.

    Args:
        session: Database session
        matter_id: Matter ID
        tenant_id: Tenant ID

    Returns:
        The matter if found, None otherwise
    """
    # Query the matter
    result = await session.execute(
        select(Matter)
        .where(Matter.id == matter_id)
        .where(Matter.tenant_id == tenant_id)
    )

    # Get the matter
    return result.scalar_one_or_none()

async def get_matter_by_id(
    matter_id: UUID,
    tenant_id: UUID,
) -> Optional[Dict[str, Any]]:
    """
    Get a matter by ID.

    Args:
        matter_id: Matter ID
        tenant_id: Tenant ID

    Returns:
        The matter if found, None otherwise
    """
    logger.info(f"Getting matter: {matter_id}")

    async with get_db() as session:
        # Get the matter
        matter = await get_matter_db(session, matter_id, tenant_id)

        # Return None if not found
        if not matter:
            return None

        # Convert to dictionary
        return {
            "id": str(matter.id),
            "tenant_id": str(matter.tenant_id),
            "title": matter.title,
            "description": matter.description,
            "status": matter.status,
            "practice_area": matter.practice_area,
            "sensitive": matter.sensitive,
            "client_id": str(matter.client_id) if matter.client_id else None,
            "created_by": str(matter.created_by) if matter.created_by else None,
            "created_at": matter.created_at.isoformat(),
            "updated_at": matter.updated_at.isoformat() if matter.updated_at else None,
            "metadata": matter.matter_metadata,
        }

async def list_matters_db(
    tenant_id: UUID,
    status: Optional[str] = None,
    client_id: Optional[UUID] = None,
    limit: int = 50,
    offset: int = 0,
) -> List[Dict[str, Any]]:
    """
    List matters with optional filtering.

    Args:
        tenant_id: Tenant ID
        status: Filter by status
        client_id: Filter by client ID
        limit: Maximum number of matters to return
        offset: Offset for pagination

    Returns:
        List of matters
    """
    logger.info(f"Listing matters for tenant: {tenant_id}")

    async with get_db() as session:
        # Build the query
        query = select(Matter).where(Matter.tenant_id == tenant_id)

        # Apply filters
        if status:
            query = query.where(Matter.status == status)

        if client_id:
            query = query.where(Matter.client_id == client_id)

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute the query
        result = await session.execute(query)

        # Get the matters
        matters = result.scalars().all()

        # Convert to dictionaries
        return [
            {
                "id": str(matter.id),
                "tenant_id": str(matter.tenant_id),
                "title": matter.title,
                "description": matter.description,
                "status": matter.status,
                "practice_area": matter.practice_area,
                "sensitive": matter.sensitive,
                "client_id": str(matter.client_id) if matter.client_id else None,
                "created_by": str(matter.created_by) if matter.created_by else None,
                "created_at": matter.created_at.isoformat(),
                "updated_at": matter.updated_at.isoformat() if matter.updated_at else None,
                "metadata": matter.matter_metadata,
            }
            for matter in matters
        ]

async def update_matter_db(
    matter_id: UUID,
    tenant_id: UUID,
    title: Optional[str] = None,
    description: Optional[str] = None,
    status: Optional[str] = None,
    practice_area: Optional[str] = None,
    sensitive: Optional[bool] = None,
    client_id: Optional[UUID] = None,
    updated_by: Optional[UUID] = None,
    matter_metadata: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    Update a matter.

    Args:
        matter_id: Matter ID
        tenant_id: Tenant ID
        title: Matter title
        description: Matter description
        status: Matter status
        practice_area: Practice area
        sensitive: Whether the matter contains sensitive information
        client_id: Client ID
        updated_by: User ID of the updater
        metadata: Additional metadata

    Returns:
        The updated matter if found, None otherwise
    """
    logger.info(f"Updating matter: {matter_id}")

    async with get_db() as session:
        # Get the matter
        matter = await get_matter_db(session, matter_id, tenant_id)

        # Return None if not found
        if not matter:
            return None

        # Update the matter
        if title is not None:
            matter.title = title

        if description is not None:
            matter.description = description

        if status is not None:
            matter.status = status

        if practice_area is not None:
            matter.practice_area = practice_area

        if sensitive is not None:
            matter.sensitive = sensitive

        if client_id is not None:
            matter.client_id = client_id

        if updated_by is not None:
            matter.updated_by = updated_by

        if matter_metadata is not None:
            matter.matter_metadata = matter_metadata

        # Set the updated_at timestamp
        matter.updated_at = datetime.now(timezone.utc)

        # Commit the transaction
        await session.commit()

        # Refresh the matter
        await session.refresh(matter)

        # Convert to dictionary
        return {
            "id": str(matter.id),
            "tenant_id": str(matter.tenant_id),
            "title": matter.title,
            "description": matter.description,
            "status": matter.status,
            "practice_area": matter.practice_area,
            "sensitive": matter.sensitive,
            "client_id": str(matter.client_id) if matter.client_id else None,
            "created_by": str(matter.created_by) if matter.created_by else None,
            "created_at": matter.created_at.isoformat(),
            "updated_at": matter.updated_at.isoformat() if matter.updated_at else None,
            "updated_by": str(matter.updated_by) if matter.updated_by else None,
            "metadata": matter.matter_metadata,
        }

async def delete_matter_db(
    matter_id: UUID,
    tenant_id: UUID,
) -> bool:
    """
    Delete a matter.

    Args:
        matter_id: Matter ID
        tenant_id: Tenant ID

    Returns:
        True if the matter was deleted, False otherwise
    """
    logger.info(f"Deleting matter: {matter_id}")

    async with get_db() as session:
        # Get the matter
        matter = await get_matter_db(session, matter_id, tenant_id)

        # Return False if not found
        if not matter:
            return False

        # Delete the matter
        await session.delete(matter)

        # Commit the transaction
        await session.commit()

        return True

# Client database functions
async def create_client_db(
    tenant_id: UUID,
    first_name: str,
    last_name: str,
    middle_name: Optional[str] = None,
    date_of_birth: Optional[date] = None,
    ssn_last_four: Optional[str] = None,
    email: Optional[str] = None,
    phone_primary: Optional[str] = None,
    phone_secondary: Optional[str] = None,
    preferred_contact_method: Optional[str] = None,
    address: Optional[Dict[str, Any]] = None,
    occupation: Optional[str] = None,
    employer: Optional[str] = None,
    notes: Optional[str] = None,
    emergency_contact: Optional[Dict[str, Any]] = None,
    created_by: Optional[UUID] = None,
    client_metadata: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    Create a new client in the database.

    Args:
        tenant_id: Tenant ID
        first_name: Client's first name
        last_name: Client's last name
        middle_name: Client's middle name
        date_of_birth: Client's date of birth
        ssn_last_four: Last four digits of SSN
        email: Client's email address
        phone_primary: Client's primary phone number
        phone_secondary: Client's secondary phone number
        preferred_contact_method: Client's preferred contact method
        address: Client's address
        occupation: Client's occupation
        employer: Client's employer
        notes: Notes about the client
        emergency_contact: Client's emergency contact information
        created_by: User ID of the creator
        metadata: Additional metadata

    Returns:
        The created client
    """
    logger.info(f"Creating client: {first_name} {last_name}")

    async with get_db() as session:
        # Create the client
        client = Client(
            tenant_id=tenant_id,
            first_name=first_name,
            last_name=last_name,
            middle_name=middle_name,
            date_of_birth=date_of_birth,
            ssn_last_four=ssn_last_four,
            email=email,
            phone_primary=phone_primary,
            phone_secondary=phone_secondary,
            preferred_contact_method=preferred_contact_method,
            address=address or {},
            occupation=occupation,
            employer=employer,
            notes=notes,
            emergency_contact=emergency_contact,
            created_by=created_by,
            created_at=datetime.now(timezone.utc),
            client_metadata=client_metadata or {},
        )

        # Add the client to the session
        session.add(client)

        # Commit the transaction
        await session.commit()

        # Refresh the client to get the generated ID
        await session.refresh(client)

        # Convert to dictionary
        return {
            "id": str(client.id),
            "tenant_id": str(client.tenant_id),
            "first_name": client.first_name,
            "last_name": client.last_name,
            "middle_name": client.middle_name,
            "date_of_birth": (
                client.date_of_birth.isoformat() if client.date_of_birth else None
            ),
            "ssn_last_four": client.ssn_last_four,
            "email": client.email,
            "phone_primary": client.phone_primary,
            "phone_secondary": client.phone_secondary,
            "preferred_contact_method": client.preferred_contact_method,
            "address": client.address,
            "occupation": client.occupation,
            "employer": client.employer,
            "notes": client.notes,
            "emergency_contact": client.emergency_contact,
            "created_by": str(client.created_by) if client.created_by else None,
            "created_at": client.created_at.isoformat(),
            "client_metadata": client.client_metadata,
        }

async def get_client_db(
    session: AsyncSession,
    client_id: UUID,
    tenant_id: UUID,
) -> Optional[Client]:
    """
    Get a client by ID.

    Args:
        session: Database session
        client_id: Client ID
        tenant_id: Tenant ID

    Returns:
        The client if found, None otherwise
    """
    # Query the client
    result = await session.execute(
        select(Client)
        .where(Client.id == client_id)
        .where(Client.tenant_id == tenant_id)
    )

    # Get the client
    return result.scalar_one_or_none()

async def get_client_by_id(
    client_id: UUID,
    tenant_id: UUID,
) -> Optional[Dict[str, Any]]:
    """
    Get a client by ID.

    Args:
        client_id: Client ID
        tenant_id: Tenant ID

    Returns:
        The client if found, None otherwise
    """
    logger.info(f"Getting client: {client_id}")

    async with get_db() as session:
        # Get the client
        client = await get_client_db(session, client_id, tenant_id)

        # Return None if not found
        if not client:
            return None

        # Convert to dictionary
        return {
            "id": str(client.id),
            "tenant_id": str(client.tenant_id),
            "first_name": client.first_name,
            "last_name": client.last_name,
            "middle_name": client.middle_name,
            "date_of_birth": (
                client.date_of_birth.isoformat() if client.date_of_birth else None
            ),
            "ssn_last_four": client.ssn_last_four,
            "email": client.email,
            "phone_primary": client.phone_primary,
            "phone_secondary": client.phone_secondary,
            "preferred_contact_method": client.preferred_contact_method,
            "address": client.address,
            "occupation": client.occupation,
            "employer": client.employer,
            "notes": client.notes,
            "emergency_contact": client.emergency_contact,
            "created_by": str(client.created_by) if client.created_by else None,
            "created_at": client.created_at.isoformat(),
            "updated_at": client.updated_at.isoformat() if client.updated_at else None,
            "client_metadata": client.client_metadata,
        }

async def list_clients_db(
    tenant_id: UUID,
    is_active: Optional[bool] = None,
    search_term: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
) -> List[Dict[str, Any]]:
    """
    List clients with optional filtering.

    Args:
        tenant_id: Tenant ID
        is_active: Filter by active status
        search_term: Search term for name, email, or phone
        limit: Maximum number of clients to return
        offset: Offset for pagination

    Returns:
        List of clients
    """
    logger.info(f"Listing clients for tenant: {tenant_id}")

    async with get_db() as session:
        # Build the query
        query = select(Client).where(Client.tenant_id == tenant_id)

        # Apply filters
        if is_active is not None:
            query = query.where(Client.is_active == is_active)

        # Apply search term if provided
        if search_term:
            search_term_lower = f"%{search_term.lower()}%"
            query = query.where(
                (Client.first_name.ilike(search_term_lower)) |
                (Client.last_name.ilike(search_term_lower)) |
                (Client.email.ilike(search_term_lower)) |
                (Client.phone_primary.ilike(search_term_lower))
            )

        # Apply pagination
        query = query.limit(limit).offset(offset)

        # Execute the query
        result = await session.execute(query)

        # Get the clients
        clients = result.scalars().all()

        # Convert to dictionaries
        return [
            {
                "id": str(client.id),
                "tenant_id": str(client.tenant_id),
                "first_name": client.first_name,
                "last_name": client.last_name,
                "middle_name": client.middle_name,
                "date_of_birth": (
                    client.date_of_birth.isoformat() if client.date_of_birth else None
                ),
                "ssn_last_four": client.ssn_last_four,
                "email": client.email,
                "phone_primary": client.phone_primary,
                "phone_secondary": client.phone_secondary,
                "preferred_contact_method": client.preferred_contact_method,
                "address": client.address,
                "occupation": client.occupation,
                "employer": client.employer,
                "notes": client.notes,
                "created_by": (
                    str(client.created_by) if client.created_by else None
                ),
                "created_at": client.created_at.isoformat(),
                "updated_at": (
                    client.updated_at.isoformat() if client.updated_at else None
                ),
                "client_metadata": client.client_metadata,
            }
            for client in clients
        ]

async def update_client_db(
    client_id: UUID,
    tenant_id: UUID,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    middle_name: Optional[str] = None,
    date_of_birth: Optional[date] = None,
    ssn_last_four: Optional[str] = None,
    email: Optional[str] = None,
    phone_primary: Optional[str] = None,
    phone_secondary: Optional[str] = None,
    preferred_contact_method: Optional[str] = None,
    address: Optional[Dict[str, Any]] = None,
    occupation: Optional[str] = None,
    employer: Optional[str] = None,
    notes: Optional[str] = None,
    emergency_contact: Optional[Dict[str, Any]] = None,
    is_active: Optional[bool] = None,
    updated_by: Optional[UUID] = None,
    client_metadata: Optional[Dict[str, Any]] = None,
) -> Optional[Dict[str, Any]]:
    """
    Update a client.

    Args:
        client_id: Client ID
        tenant_id: Tenant ID
        first_name: Client's first name
        last_name: Client's last name
        middle_name: Client's middle name
        date_of_birth: Client's date of birth
        ssn_last_four: Last four digits of SSN
        email: Client's email address
        phone_primary: Client's primary phone number
        phone_secondary: Client's secondary phone number
        preferred_contact_method: Client's preferred contact method
        address: Client's address
        occupation: Client's occupation
        employer: Client's employer
        notes: Notes about the client
        emergency_contact: Client's emergency contact information
        is_active: Whether the client is active
        updated_by: User ID of the updater
        metadata: Additional metadata

    Returns:
        The updated client if found, None otherwise
    """
    logger.info(f"Updating client: {client_id}")

    async with get_db() as session:
        # Get the client
        client = await get_client_db(session, client_id, tenant_id)

        # Return None if not found
        if not client:
            return None

        # Update the client
        if first_name is not None:
            client.first_name = first_name

        if last_name is not None:
            client.last_name = last_name

        if middle_name is not None:
            client.middle_name = middle_name

        if date_of_birth is not None:
            client.date_of_birth = date_of_birth

        if ssn_last_four is not None:
            client.ssn_last_four = ssn_last_four

        if email is not None:
            client.email = email

        if phone_primary is not None:
            client.phone_primary = phone_primary

        if phone_secondary is not None:
            client.phone_secondary = phone_secondary

        if preferred_contact_method is not None:
            client.preferred_contact_method = preferred_contact_method

        if address is not None:
            client.address = address

        if occupation is not None:
            client.occupation = occupation

        if employer is not None:
            client.employer = employer

        if notes is not None:
            client.notes = notes

        if emergency_contact is not None:
            client.emergency_contact = emergency_contact

        if is_active is not None:
            client.is_active = is_active

        if updated_by is not None:
            client.updated_by = updated_by

        if client_metadata is not None:
            client.client_metadata = client_metadata

        # Set the updated_at timestamp
        client.updated_at = datetime.now(timezone.utc)

        # Commit the transaction
        await session.commit()

        # Refresh the client
        await session.refresh(client)

        # Convert to dictionary
        return {
            "id": str(client.id),
            "tenant_id": str(client.tenant_id),
            "first_name": client.first_name,
            "last_name": client.last_name,
            "middle_name": client.middle_name,
            "date_of_birth": (
                client.date_of_birth.isoformat() if client.date_of_birth else None
            ),
            "ssn_last_four": client.ssn_last_four,
            "email": client.email,
            "phone_primary": client.phone_primary,
            "phone_secondary": client.phone_secondary,
            "preferred_contact_method": client.preferred_contact_method,
            "address": client.address,
            "occupation": client.occupation,
            "employer": client.employer,
            "notes": client.notes,
            "emergency_contact": client.emergency_contact,
            "is_active": client.is_active,
            "created_by": str(client.created_by) if client.created_by else None,
            "created_at": client.created_at.isoformat(),
            "updated_at": client.updated_at.isoformat() if client.updated_at else None,
            "updated_by": str(client.updated_by) if client.updated_by else None,
            "client_metadata": client.client_metadata,
        }

async def delete_client_db(
    client_id: UUID,
    tenant_id: UUID,
) -> bool:
    """
    Delete a client.

    Args:
        client_id: Client ID
        tenant_id: Tenant ID

    Returns:
        True if the client was deleted, False otherwise
    """
    logger.info(f"Deleting client: {client_id}")

    async with get_db() as session:
        # Get the client
        client = await get_client_db(session, client_id, tenant_id)

        # Return False if not found
        if not client:
            return False

        # Delete the client
        await session.delete(client)

        # Commit the transaction
        await session.commit()

        return True