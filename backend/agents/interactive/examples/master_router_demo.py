#!/usr/bin/env python3
"""
Master Router Demo

This script demonstrates the master router functionality for routing
user intents to appropriate agents, with a focus on calendar routing.
"""

import asyncio
import logging

from langchain_core.messages import HumanMessage

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Import the master router
from backend.agents.interactive.master_router import _is_calendar_intent, master_router


async def demo_master_router():
    """Demonstrate master router functionality with various user inputs."""
    
    print("🤖 Master Router Demo")
    print("=" * 50)
    
    # Test cases for different intents
    test_cases = [
        # Calendar intents
        {
            "input": "Schedule a meeting with <PERSON> tomorrow at 2pm",
            "expected": "calendar_graph",
            "description": "Calendar creation intent"
        },
        {
            "input": "Show my calendar for next week",
            "expected": "calendar_graph", 
            "description": "Calendar viewing intent"
        },
        {
            "input": "Reschedule the meeting with client",
            "expected": "calendar_graph",
            "description": "Calendar update intent"
        },
        {
            "input": "Cancel my appointment tomorrow",
            "expected": "calendar_graph",
            "description": "Calendar deletion intent"
        },
        {
            "input": "When am I free next Tuesday?",
            "expected": "calendar_graph",
            "description": "Calendar availability intent"
        },
        {
            "input": "calendar.create new event",
            "expected": "calendar_graph",
            "description": "Explicit calendar namespace"
        },
        
        # Other agent intents
        {
            "input": "matter.create new personal injury matter",
            "expected": "matter_client_agent",
            "description": "Matter creation intent"
        },
        {
            "input": "client.update contact information",
            "expected": "matter_client_agent",
            "description": "Client update intent"
        },
        {
            "input": "task.create follow up with client",
            "expected": "task_crud_agent",
            "description": "Task creation intent"
        },
        {
            "input": "Research case law on negligence",
            "expected": "research_agent",
            "description": "Research intent"
        },
        {
            "input": "New client intake for car accident",
            "expected": "intake_agent",
            "description": "Intake intent"
        },
        {
            "input": "Hello, how can you help me?",
            "expected": "supervisor_agent",
            "description": "General/unknown intent"
        }
    ]
    
    print(f"Testing {len(test_cases)} routing scenarios...\n")
    
    correct_routes = 0
    total_routes = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        user_input = test_case["input"]
        expected_route = test_case["expected"]
        description = test_case["description"]
        
        # Create state with the user input
        state = {
            "messages": [HumanMessage(content=user_input)],
            "tenant_id": "demo-tenant",
            "user_id": "demo-user"
        }
        
        # Route the request
        try:
            result = await master_router(state, {})
            actual_route = result["next"]
            
            # Check if routing is correct
            is_correct = actual_route == expected_route
            if is_correct:
                correct_routes += 1
                status = "✅ PASS"
            else:
                status = "❌ FAIL"
            
            print(f"{i:2d}. {status} {description}")
            print(f"    Input: '{user_input}'")
            print(f"    Expected: {expected_route}")
            print(f"    Actual:   {actual_route}")
            
            # Show calendar intent detection details for calendar cases
            if "calendar" in expected_route:
                is_calendar = _is_calendar_intent(user_input)
                print(f"    Calendar Intent Detected: {is_calendar}")
            
            print()
            
        except Exception as e:
            print(f"{i:2d}. ❌ ERROR {description}")
            print(f"    Input: '{user_input}'")
            print(f"    Error: {str(e)}")
            print()
    
    # Summary
    print("=" * 50)
    print(f"📊 Results: {correct_routes}/{total_routes} correct routes ({correct_routes/total_routes*100:.1f}%)")
    
    if correct_routes == total_routes:
        print("🎉 All routing tests passed!")
    else:
        print(f"⚠️  {total_routes - correct_routes} routing tests failed")
    
    return correct_routes == total_routes


def demo_calendar_intent_detection():
    """Demonstrate calendar intent detection functionality."""
    
    print("\n🗓️  Calendar Intent Detection Demo")
    print("=" * 50)
    
    # Test calendar intent detection
    calendar_examples = [
        "Schedule a meeting",
        "Book an appointment", 
        "Show my calendar",
        "Reschedule the meeting",
        "Cancel appointment",
        "When am I free?",
        "calendar.create event"
    ]
    
    non_calendar_examples = [
        "Hello world",
        "Research case law",
        "Create a new case",
        "task.create reminder",
        "What's the weather?"
    ]
    
    print("Calendar Intent Examples:")
    for example in calendar_examples:
        is_calendar = _is_calendar_intent(example)
        status = "✅" if is_calendar else "❌"
        print(f"  {status} '{example}' → {is_calendar}")
    
    print("\nNon-Calendar Intent Examples:")
    for example in non_calendar_examples:
        is_calendar = _is_calendar_intent(example)
        status = "❌" if is_calendar else "✅"
        print(f"  {status} '{example}' → {is_calendar}")


async def main():
    """Main demo function."""
    print("🚀 Starting Master Router Demo\n")
    
    # Run the routing demo
    routing_success = await demo_master_router()
    
    # Run the calendar intent detection demo
    demo_calendar_intent_detection()
    
    print("\n" + "=" * 50)
    if routing_success:
        print("✅ Demo completed successfully!")
    else:
        print("❌ Demo completed with some failures")
    
    print("🔗 Master Router successfully routes calendar intents to calendar_graph")


if __name__ == "__main__":
    asyncio.run(main())
