"""
Tests for Master Router

This module contains comprehensive tests for the master router functionality,
including intent detection, routing logic, and integration with the calendar graph.
"""

from unittest.mock import Magic<PERSON>ock, patch

import pytest
from langchain_core.messages import HumanMessage

# Mock the problematic imports before importing the master router
with patch.dict('sys.modules', {
    'backend.agents.interactive.calendar_crud.graph': <PERSON><PERSON><PERSON>(),
    'backend.agents.interactive.calendar_crud.agent': <PERSON><PERSON>ock(),
    'backend.agents.interactive.calendar_crud.nodes': <PERSON><PERSON><PERSON>(),
    'pi_lawyer.agents.base_agent': <PERSON><PERSON>ock(),
    'pi_lawyer.agents.interactive': MagicMock(),
}):
    from backend.agents.interactive.master_router import (
        _is_calendar_intent,
        get_workflow_info,
        master_router,
    )


class TestMasterRouter:
    """Test cases for the master router function."""

    @pytest.mark.asyncio
    async def test_master_router_calendar_intent(self):
        """Test master router with calendar intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="Schedule a meeting with <PERSON> tomorrow at 2pm")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "calendar_graph"}

    @pytest.mark.asyncio
    async def test_master_router_case_intent(self):
        """Test master router with case intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="case.create new personal injury case")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "matter_client_agent"}

    @pytest.mark.asyncio
    async def test_master_router_client_intent(self):
        """Test master router with client intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="client.update contact information")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "matter_client_agent"}

    @pytest.mark.asyncio
    async def test_master_router_task_intent(self):
        """Test master router with task intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="task.create follow up with client")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "task_graph"}

    @pytest.mark.asyncio
    async def test_master_router_research_intent(self):
        """Test master router with research intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="Research case law on personal injury claims")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "research_agent"}

    @pytest.mark.asyncio
    async def test_master_router_intake_intent(self):
        """Test master router with intake intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="New client intake for car accident case")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "intake_agent"}

    @pytest.mark.asyncio
    async def test_master_router_no_messages(self):
        """Test master router with no messages."""
        # Arrange
        state = {
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "supervisor_agent"}

    @pytest.mark.asyncio
    async def test_master_router_empty_messages(self):
        """Test master router with empty messages list."""
        # Arrange
        state = {
            "messages": [],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "supervisor_agent"}

    @pytest.mark.asyncio
    async def test_master_router_no_user_input(self):
        """Test master router with no user input in messages."""
        # Arrange
        state = {
            "messages": [{"type": "system", "content": "System message"}],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "supervisor_agent"}

    @pytest.mark.asyncio
    async def test_master_router_unknown_intent(self):
        """Test master router with unknown intent."""
        # Arrange
        state = {
            "messages": [HumanMessage(content="Hello, how are you?")],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "supervisor_agent"}

    @pytest.mark.asyncio
    async def test_master_router_dict_message_format(self):
        """Test master router with dictionary message format."""
        # Arrange
        state = {
            "messages": [{"type": "human", "content": "Schedule a meeting tomorrow"}],
            "tenant_id": "test-tenant",
            "user_id": "test-user",
        }
        config = {}
        
        # Act
        result = await master_router(state, config)
        
        # Assert
        assert result == {"next": "calendar_graph"}


class TestCalendarIntentDetection:
    """Test cases for calendar intent detection."""

    def test_calendar_intent_schedule_keywords(self):
        """Test calendar intent detection with schedule keywords."""
        test_cases = [
            "Schedule a meeting with John",
            "Book an appointment for tomorrow",
            "Create meeting for next week",
            "Set appointment with client",
            "Add event to calendar",
            "Plan meeting for Friday",
            "Arrange a conference call",
            "Organize meeting with team"
        ]
        
        for test_case in test_cases:
            assert _is_calendar_intent(test_case), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_view_keywords(self):
        """Test calendar intent detection with view keywords."""
        test_cases = [
            "Show my calendar",
            "View calendar for next week",
            "Check my schedule",
            "What's on my calendar today?",
            "Calendar events for tomorrow",
            "Upcoming meetings",
            "Today's schedule",
            "This week's schedule"
        ]
        
        for test_case in test_cases:
            assert _is_calendar_intent(test_case), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_update_keywords(self):
        """Test calendar intent detection with update keywords."""
        test_cases = [
            "Reschedule the meeting",
            "Move meeting to tomorrow",
            "Change appointment time",
            "Update event details",
            "Modify meeting time",
            "Edit appointment"
        ]
        
        for test_case in test_cases:
            assert _is_calendar_intent(test_case), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_delete_keywords(self):
        """Test calendar intent detection with delete keywords."""
        test_cases = [
            "Cancel the meeting",
            "Delete event from calendar",
            "Remove appointment",
            "Cancel appointment with client"
        ]
        
        for test_case in test_cases:
            assert _is_calendar_intent(test_case), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_availability_keywords(self):
        """Test calendar intent detection with availability keywords."""
        test_cases = [
            "When am I free tomorrow?",
            "Available time next week",
            "Check my free time",
            "When can we meet?",
            "Find time for meeting",
            "Check availability"
        ]
        
        for test_case in test_cases:
            assert _is_calendar_intent(test_case), f"Failed to detect calendar intent in: {test_case}"

    def test_calendar_intent_explicit_namespace(self):
        """Test calendar intent detection with explicit namespace."""
        test_cases = [
            "calendar.create new event",
            "calendar.list events",
            "calendar.update meeting time",
            "calendar.delete appointment"
        ]
        
        for test_case in test_cases:
            assert _is_calendar_intent(test_case), f"Failed to detect calendar intent in: {test_case}"

    def test_non_calendar_intent(self):
        """Test that non-calendar intents are not detected as calendar intents."""
        test_cases = [
            "Hello, how are you?",
            "Research case law",
            "Create a new case",
            "Update client information",
            "Draft a document",
            "What is the weather like?",
            "task.create follow up"
        ]
        
        for test_case in test_cases:
            assert not _is_calendar_intent(test_case), f"Incorrectly detected calendar intent in: {test_case}"


class TestMasterGraphCreation:
    """Test cases for master graph creation."""

    def test_get_workflow_info(self):
        """Test workflow info retrieval."""
        # Act
        info = get_workflow_info()

        # Assert
        assert info["name"] == "master_routing_workflow"
        assert info["version"] == "1.0.0"
        assert "master_router" in info["nodes"]
        assert "calendar_graph" in info["nodes"]
        assert "task_graph" in info["nodes"]
        assert info["entry_point"] == "master_router"
        assert "Intent detection and routing" in info["capabilities"]
        assert "calendar.*" in info["supported_intents"]
