"""
SQLAlchemy model for firms (tenants) in the PI Lawyer AI system.

This model represents the tenants.firms table which stores law firm information
and includes MCP-related fields for automated key provisioning.
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID, uuid4

from sqlalchemy import (
    CheckConstraint,
    DateTime,
    String,
    Text,
    Integer,
    ARRAY,
    func,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base


class Firm(Base):
    """SQLAlchemy model for firms (tenants)."""

    __tablename__ = "firms"
    __table_args__ = (
        CheckConstraint(
            "firm_type IN ('Solo Practice', 'Partnership', 'Professional Corporation', 'Limited Liability Company')",
            name="firms_firm_type_check"
        ),
        CheckConstraint(
            "status IN ('active', 'inactive', 'suspended')",
            name="firms_status_check"
        ),
        CheckConstraint(
            "verification_status IN ('pending', 'verified', 'rejected')",
            name="firms_verification_status_check"
        ),
        CheckConstraint(
            "subscription_tier IN ('basic', 'professional', 'enterprise')",
            name="firms_subscription_tier_check"
        ),
        CheckConstraint(
            "subscription_status IN ('active', 'past_due', 'canceled')",
            name="firms_subscription_status_check"
        ),
        CheckConstraint(
            "mcp_status IN ('pending_key', 'active', 'inactive', 'key_provisioning_failed')",
            name="firms_mcp_status_check"
        ),
        CheckConstraint(
            "primary_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'",
            name="firms_valid_primary_email"
        ),
        {"schema": "tenants"},
    )

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Tenant isolation
    tenant_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=False, index=True
    )

    # Basic Firm Information
    name: Mapped[str] = mapped_column(Text, nullable=False)
    state_bar_number: Mapped[str] = mapped_column(Text, nullable=False, unique=True)
    firm_type: Mapped[str] = mapped_column(Text, nullable=False)
    year_established: Mapped[Optional[int]] = mapped_column(Integer)
    tax_id: Mapped[Optional[str]] = mapped_column(Text)

    # Contact Information
    primary_email: Mapped[str] = mapped_column(Text, nullable=False)
    secondary_email: Mapped[Optional[str]] = mapped_column(Text)
    phone: Mapped[str] = mapped_column(Text, nullable=False)
    fax: Mapped[Optional[str]] = mapped_column(Text)
    website_url: Mapped[Optional[str]] = mapped_column(Text)

    # Address Information (JSONB)
    address: Mapped[Dict[str, Any]] = mapped_column(
        JSONB, nullable=False, default=dict
    )

    # Practice Areas & Specializations
    practice_areas: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(Text), default=list
    )
    specializations: Mapped[Optional[List[str]]] = mapped_column(
        ARRAY(Text), default=list
    )

    # Administrative Details
    admin_user_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True), index=True
    )
    status: Mapped[str] = mapped_column(
        Text, nullable=False, default="active"
    )
    verification_status: Mapped[str] = mapped_column(
        Text, nullable=False, default="pending"
    )

    # Subscription & Billing
    subscription_tier: Mapped[str] = mapped_column(
        Text, nullable=False, default="basic"
    )
    subscription_status: Mapped[str] = mapped_column(
        Text, nullable=False, default="active"
    )

    # MCP-related fields for automated key provisioning
    mcp_secret_path: Mapped[Optional[str]] = mapped_column(
        Text, index=True
    )
    mcp_status: Mapped[str] = mapped_column(
        Text, nullable=False, default="pending_key"
    )

    # Metadata & Settings
    settings: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, default=dict
    )
    firm_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSONB, default=dict
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False
    )

    def __repr__(self) -> str:
        return f"<Firm(id={self.id}, name='{self.name}', mcp_status='{self.mcp_status}')>"

    @property
    def is_mcp_ready(self) -> bool:
        """Check if the firm has MCP API key provisioned and is active."""
        return self.mcp_status == "active" and self.mcp_secret_path is not None

    @property
    def needs_mcp_provisioning(self) -> bool:
        """Check if the firm needs MCP key provisioning."""
        return self.mcp_status == "pending_key" and self.mcp_secret_path is None

    def get_mcp_secret_name(self) -> Optional[str]:
        """Extract the secret name from the full secret path."""
        if not self.mcp_secret_path:
            return None
        
        # Extract secret name from path like: projects/PROJECT_ID/secrets/SECRET_NAME
        parts = self.mcp_secret_path.split('/')
        if len(parts) >= 4 and parts[-2] == 'secrets':
            return parts[-1]
        return None
