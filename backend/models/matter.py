"""
SQLAlchemy models for matters (formerly cases).

This module defines the SQLAlchemy models for matters in the PI Lawyer AI system.
Matters represent legal work that can be litigation, transactional, advisory, or IP-related.
"""

from datetime import datetime
from typing import Optional
from uuid import UUID, uuid4

from sqlalchemy import <PERSON><PERSON><PERSON>, Boolean, DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class Matter(Base):
    """SQLAlchemy model for matters (legal work items)."""

    __tablename__ = "matters"
    __table_args__ = {"schema": "tenants"}

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), primary_key=True, default=uuid4
    )

    # Tenant isolation
    tenant_id: Mapped[UUID] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=False, index=True
    )

    # Basic matter information
    title: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    status: Mapped[str] = mapped_column(
        String(50), nullable=False, default="active"
    )
    
    # Practice area determines the display label (Case vs Matter)
    practice_area: Mapped[str] = mapped_column(
        String(50), nullable=False, default="litigation"
    )
    
    # Computed display label (handled by database)
    display_label: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)
    
    sensitive: Mapped[bool] = mapped_column(Boolean, default=False)

    # Relationships
    client_id: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True), ForeignKey("tenants.clients.id"), nullable=True
    )
    client = relationship("Client", back_populates="matters")

    # Audit fields
    created_by: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=True
    )
    created_at: Mapped[datetime] = mapped_column(
        DateTime, nullable=False, default=datetime.utcnow
    )
    updated_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime, nullable=True, onupdate=datetime.utcnow
    )
    updated_by: Mapped[Optional[UUID]] = mapped_column(
        PostgresUUID(as_uuid=True), nullable=True
    )

    # Additional data
    metadata: Mapped[dict] = mapped_column(
        JSON, nullable=False, default=dict
    )
    
    # Rejection reason for matters that are rejected
    rejection_reason: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    def __repr__(self) -> str:
        """Return string representation of the matter."""
        return f"<Matter(id={self.id}, title={self.title}, practice_area={self.practice_area}, status={self.status})>"

    @property
    def is_litigation(self) -> bool:
        """Check if this matter is litigation-related."""
        return self.practice_area == "litigation"

    @property
    def computed_display_label(self) -> str:
        """Get the display label for this matter."""
        return "Case" if self.is_litigation else "Matter"


# Backward compatibility alias
Case = Matter
