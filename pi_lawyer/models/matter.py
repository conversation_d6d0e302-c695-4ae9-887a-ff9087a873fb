"""
Matter models for PI Lawyer AI.

These models define the structure of matter data in the application, including
validation rules and serialization/deserialization methods.
Matters represent legal work that can be litigation, transactional, advisory, or IP-related.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from uuid import UUID
from pydantic import BaseModel, Field


class PracticeArea(str, Enum):
    """Enum representing practice areas."""

    LITIGATION = "litigation"
    TRANSACTION = "transaction"
    ADVISORY = "advisory"
    IP = "ip"


class MatterStatus(str, Enum):
    """Enum representing matter statuses."""

    ACTIVE = "active"
    PENDING = "pending"
    SETTLED = "settled"
    CLOSED = "closed"
    ARCHIVED = "archived"
    REJECTED = "rejected"


class MatterBase(BaseModel):
    """Base model with common matter attributes."""

    title: str = Field(..., description="Title of the matter")
    description: Optional[str] = Field(
        None, description="Detailed description of the matter"
    )
    status: MatterStatus = Field(
        MatterStatus.ACTIVE, description="Current status of the matter"
    )
    practice_area: PracticeArea = Field(
        PracticeArea.LITIGATION, description="Practice area of the matter"
    )
    sensitive: bool = Field(
        False, description="Whether the matter contains sensitive information"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Additional metadata for the matter"
    )

    @property
    def display_label(self) -> str:
        """Get the display label for this matter."""
        return "Case" if self.practice_area == PracticeArea.LITIGATION else "Matter"


class MatterCreate(MatterBase):
    """Model for creating a new matter."""

    tenant_id: UUID = Field(
        ..., description="ID of the firm/tenant that owns this matter"
    )
    client_id: Optional[UUID] = Field(
        None, description="ID of the client associated with this matter"
    )
    created_by: Optional[UUID] = Field(
        None, description="ID of the user who created this matter"
    )


class MatterUpdate(BaseModel):
    """Model for updating an existing matter."""

    title: Optional[str] = Field(None, description="Title of the matter")
    description: Optional[str] = Field(
        None, description="Detailed description of the matter"
    )
    status: Optional[MatterStatus] = Field(None, description="Current status of the matter")
    practice_area: Optional[PracticeArea] = Field(None, description="Practice area of the matter")
    sensitive: Optional[bool] = Field(
        None, description="Whether the matter contains sensitive information"
    )
    client_id: Optional[UUID] = Field(
        None, description="ID of the client associated with this matter"
    )
    metadata: Optional[Dict[str, Any]] = Field(
        None, description="Additional metadata for the matter"
    )
    updated_by: Optional[UUID] = Field(
        None, description="ID of the user who updated this matter"
    )
    rejection_reason: Optional[str] = Field(
        None, description="Reason for rejection if status is rejected"
    )


class Matter(MatterBase):
    """Complete matter model with all attributes."""

    id: UUID = Field(..., description="Unique identifier for the matter")
    tenant_id: UUID = Field(
        ..., description="ID of the firm/tenant that owns this matter"
    )
    client_id: Optional[UUID] = Field(
        None, description="ID of the client associated with this matter"
    )
    created_by: Optional[UUID] = Field(
        None, description="ID of the user who created this matter"
    )
    created_at: datetime = Field(
        default_factory=datetime.now, description="When the matter was created"
    )
    updated_at: Optional[datetime] = Field(
        None, description="When the matter was last updated"
    )
    rejection_reason: Optional[str] = Field(
        None, description="Reason for rejection if status is rejected"
    )

    class Config:
        """Pydantic model configuration."""

        from_attributes = True  # Support ORM mode
        json_schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "tenant_id": "123e4567-e89b-12d3-a456-************",
                "title": "Jones v. Smith Car Accident",
                "description": "Personal injury case from collision on Highway 35",
                "status": "active",
                "practice_area": "litigation",
                "sensitive": False,
                "client_id": "123e4567-e89b-12d3-a456-426614174002",
                "created_by": "123e4567-e89b-12d3-a456-426614174003",
                "created_at": "2025-01-01T00:00:00Z",
                "updated_at": "2025-01-02T00:00:00Z",
                "metadata": {"priority": "high", "case_type": "auto_accident"},
            }
        }


# Backward compatibility aliases
CaseStatus = MatterStatus
CaseType = PracticeArea  # Map old CaseType to PracticeArea
CaseBase = MatterBase
CaseCreate = MatterCreate
CaseUpdate = MatterUpdate
Case = Matter
