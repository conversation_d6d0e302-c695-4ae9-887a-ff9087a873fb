'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import {
  LayoutDashboard,
  Users,
  Briefcase,
  FileText,
  Calendar,
  Settings,
  MessageSquarePlus,
  ChevronLeft,
  ChevronRight,
  PlusCircle,
  Clock,
  BookOpen,
  FolderOpen,
} from 'lucide-react'

import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider
} from '@/components/ui/tooltip'
import { useMattersApi } from '@/hooks/useMattersApi'
import { PracticeArea } from '@/types/domain/tenants/Matter'

interface SidebarProps {
  isOpen: boolean
  onToggle: () => void
  onChatOpen: () => void
}

interface NavItem {
  title: string
  href: string
  icon: React.ElementType
  aiCommand?: string
}

/**
 * Hook to determine the appropriate navigation label for matters/cases
 * Returns "Cases" if user has only litigation matters, "Matters" if mixed practice areas
 */
function useNavigationLabel() {
  const { getAllMatters } = useMattersApi()
  const [navigationLabel, setNavigationLabel] = useState('Cases') // Default to Cases for backward compatibility
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const determineLabel = async () => {
      try {
        const matters = await getAllMatters({ limit: 100 }) // Get a reasonable sample

        if (matters.length === 0) {
          setNavigationLabel('Cases') // Default for new users
          return
        }

        // Check if all matters are litigation
        const allLitigation = matters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)

        if (allLitigation) {
          setNavigationLabel('Cases')
        } else {
          // Mixed practice areas - use "Matters"
          setNavigationLabel('Matters')
        }
      } catch (error) {
        console.error('Error determining navigation label:', error)
        setNavigationLabel('Cases') // Fallback to Cases on error
      } finally {
        setIsLoading(false)
      }
    }

    determineLabel()
  }, [getAllMatters])

  return { navigationLabel, isLoading }
}

/**
 * Generate navigation items with dynamic labeling
 */
function getMainNavItems(navigationLabel: string): NavItem[] {
  return [
    {
      title: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
      aiCommand: 'Show me my dashboard overview',
    },
    {
      title: 'Clients',
      href: '/clients',
      icon: Users,
      aiCommand: 'List my active clients',
    },
    {
      title: 'New Intake',
      href: '/clients/intake',
      icon: PlusCircle,
      aiCommand: 'Start a new client intake',
    },
    {
      title: navigationLabel,
      href: '/cases', // Use cases route (backend handles matters data)
      icon: Briefcase,
      aiCommand: navigationLabel === 'Cases' ? 'Show my current cases' : 'Show my current matters',
    },
    {
      title: 'Drafting',
      href: '/documents',
      icon: FileText,
      aiCommand: 'Help me draft legal documents',
    },
    {
      title: 'Document Center',
      href: '/document-center',
      icon: FolderOpen,
      aiCommand: 'Manage my uploaded documents',
    },
    {
      title: 'Legal Research',
      href: '/research',
      icon: BookOpen,
      aiCommand: 'Help me research legal precedents',
    },
    {
      title: 'Calendar',
      href: '/calendar',
      icon: Calendar,
      aiCommand: 'Show my upcoming deadlines',
    },
    {
      title: 'Tasks',
      href: '/tasks',
      icon: Clock,
      aiCommand: 'Show my tasks list',
    },
  ]
}

export function Sidebar({ isOpen, onToggle, onChatOpen }: SidebarProps) {
  const pathname = usePathname()
  const router = useRouter()
  const [firmName, setFirmName] = useState('Law Firm Name') // TODO: Fetch from Supabase
  const { navigationLabel, isLoading } = useNavigationLabel()

  // Generate navigation items with dynamic labeling
  const mainNavItems = getMainNavItems(navigationLabel)

  // Generate quick actions with dynamic labeling
  const quickActions = [
    {
      title: navigationLabel === 'Cases' ? 'New Case' : 'New Matter',
      icon: PlusCircle,
      aiCommand: navigationLabel === 'Cases' ? 'Create a new case' : 'Create a new matter',
    },
    {
      title: 'Schedule',
      icon: Clock,
      aiCommand: 'Schedule a new deadline',
    },
  ]

  // Handle actions directly without CopilotKit
  const handleAction = (actionTitle: string) => {
    if (actionTitle === 'New Case' || actionTitle === 'New Matter') {
      router.push('/cases/new')
    } else if (actionTitle === 'Schedule') {
      router.push('/calendar')
    }
  }

  return (
    <>
      {/* Mobile backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <motion.div
        className={cn(
          'fixed top-0 left-0 z-50 h-full bg-card border-r',
          isOpen ? 'w-64' : 'w-20',
          'transition-all duration-300 ease-in-out'
        )}
        initial={false}
        animate={{ width: isOpen ? 256 : 80 }}
      >
        {/* Logo & Toggle */}
        <div className="flex items-center justify-between h-16 px-4 border-b">
          {isOpen ? (
            <h2 className="text-lg font-semibold">{firmName}</h2>
          ) : (
            <div className="w-8 h-8 rounded-full bg-primary" /> // Logo placeholder
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="lg:flex hidden"
          >
            {isOpen ? (
              <ChevronLeft className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Quick Actions */}
        <div className="p-4 border-b">
          <div className="space-y-2">
            {quickActions.map((action) => (
              <TooltipProvider key={action.title}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start',
                        !isOpen && 'justify-center p-2'
                      )}
                      onClick={() => {
                        handleAction(action.title)
                      }}
                    >
                      <action.icon className="h-4 w-4" />
                      {isOpen && <span className="ml-2">{action.title}</span>}
                    </Button>
                  </TooltipTrigger>
                  {!isOpen && (
                    <TooltipContent side="right">
                      {action.title}
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            ))}
          </div>
        </div>

        {/* Main Navigation */}
        <nav className="space-y-1 p-4">
          {mainNavItems.map((item) => (
            <TooltipProvider key={item.title}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link
                    href={item.href}
                    className={cn(
                      'flex items-center px-4 py-2 text-sm font-medium rounded-md',
                      pathname === item.href
                        ? 'bg-primary/10 text-primary'
                        : 'text-muted-foreground hover:bg-accent',
                      !isOpen && 'justify-center px-2'
                    )}
                  >
                    <item.icon className="h-4 w-4" />
                    {isOpen && <span className="ml-3">{item.title}</span>}
                  </Link>
                </TooltipTrigger>
                {!isOpen && (
                  <TooltipContent side="right">{item.title}</TooltipContent>
                )}
              </Tooltip>
            </TooltipProvider>
          ))}
        </nav>

        {/* AI Chat Button */}
        <div className="absolute bottom-4 left-0 right-0 px-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="default"
                  className={cn(
                    'w-full justify-start',
                    !isOpen && 'justify-center p-2'
                  )}
                  onClick={onChatOpen}
                >
                  <MessageSquarePlus className="h-4 w-4" />
                  {isOpen && <span className="ml-2">Ask Ailex</span>}
                </Button>
              </TooltipTrigger>
              {!isOpen && (
                <TooltipContent side="right">Ask Ailex</TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </div>
      </motion.div>
    </>
  )
}
