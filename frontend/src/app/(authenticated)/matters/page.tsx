'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

/**
 * Redirect page for /matters route
 * Redirects users to the main /cases route which displays matters data
 */
export default function MattersRedirectPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to cases page (which displays matters data with dynamic labeling)
    router.replace('/cases')
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Redirecting...</h1>
        <p className="text-muted-foreground">
          Taking you to your cases and matters...
        </p>
      </div>
    </div>
  )
}
