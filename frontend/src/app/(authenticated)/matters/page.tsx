'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import {
  Briefcase,
  Plus,
  Search,
  Filter,
  Calendar,
  Users,
  FileText,
  RefreshCw,
  MoreHorizontal,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/components/ui/use-toast'
import { useSupabase } from '@/lib/supabase/provider'
import { useMattersApi } from '@/hooks/useMattersApi'
import { Matter, PracticeArea, getMatterDisplayLabel } from '@/types/domain/tenants/Matter'

// Status and practice area options
const MATTER_STATUSES = [
  { id: 'all', label: 'All Statuses' },
  { id: 'active', label: 'Active' },
  { id: 'pending', label: 'Pending' },
  { id: 'inactive', label: 'Inactive' },
  { id: 'closed', label: 'Closed' }
];

const PRACTICE_AREAS = [
  { id: 'all', label: 'All Practice Areas' },
  { id: 'personal-injury', label: 'Personal Injury' },
  { id: 'medical-malpractice', label: 'Medical Malpractice' },
  { id: 'employment', label: 'Employment' },
  { id: 'real-estate', label: 'Real Estate' }
];

export default function MattersPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { getAllMatters, getMatterStats } = useMattersApi()

  // States
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [practiceAreaFilter, setPracticeAreaFilter] = useState('all')
  const [matters, setMatters] = useState<Matter[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [navigationLabel, setNavigationLabel] = useState('Matters') // Dynamic label

  // Stats
  const [stats, setStats] = useState({
    totalMatters: 0,
    activeMatters: 0,
    upcomingDeadlines: 0,
    recentDocuments: 0
  })

  // Fetch matters and stats using new Matter services
  const fetchData = useCallback(async () => {
    setLoading(true);

    try {
      // Build filters for Matter API
      const filters: Record<string, unknown> = {
        page,
        limit: 10
      };

      if (searchQuery) {
        filters.search = searchQuery;
      }

      if (statusFilter !== 'all') {
        filters.status = statusFilter;
      }

      if (practiceAreaFilter !== 'all') {
        filters.practice_area = practiceAreaFilter;
      }

      // Fetch all matters
      const mattersData = await getAllMatters(filters);
      setMatters(mattersData);

      // Determine navigation label based on user's matters
      const allMatters = await getAllMatters({ limit: 50 });
      const allLitigation = allMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION);
      setNavigationLabel(allLitigation ? 'Cases' : 'Matters');

      // Fetch stats
      const statsData = await getMatterStats();
      if (statsData) {
        setStats({
          totalMatters: (statsData as Record<string, unknown>)?.total as number || 0,
          activeMatters: (statsData as Record<string, unknown>)?.active as number || 0,
          upcomingDeadlines: (statsData as Record<string, unknown>)?.upcomingDeadlines as number || 0,
          recentDocuments: (statsData as Record<string, unknown>)?.recentDocuments as number || 0
        });
      }

      // For now, assume single page (can be enhanced later)
      setTotalPages(Math.ceil((mattersData.length || 0) / 10));

    } catch (error) {
      console.error('Failed to fetch matters data:', error)
      // Check if error is an instance of Error before accessing message
      const errorMessage = error instanceof Error ? error.message : 'Failed to load matters';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      })
      // Set empty matters on error
      setMatters([])
      setTotalPages(1)
    } finally {
      setLoading(false)
    }
  }, [searchQuery, statusFilter, practiceAreaFilter, page, getAllMatters, getMatterStats, toast]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Handle search
  const handleSearch = () => {
    setPage(1) // Reset to first page when searching
  }

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'None'

    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
    } catch (e) {
      console.error('Date parsing error:', e)
      return 'Invalid date'
    }
  }

  // Calculate days from now
  const getDaysFromNow = (dateString: string | null) => {
    if (!dateString) return null

    const now = new Date()
    const targetDate = new Date(dateString)
    const diffTime = targetDate.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays
  }

  // Render status badge
  const renderStatusBadge = (status: string) => {
    switch(status) {
      case 'active':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Active</Badge>
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending</Badge>
      case 'inactive':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Inactive</Badge>
      case 'closed':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Closed</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  // Render priority indicator
  const renderPriority = (priority: string) => {
    switch(priority) {
      case 'high':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>High</div>
      case 'medium':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>Medium</div>
      case 'low':
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>Low</div>
      default:
        return <div className="flex items-center"><div className="w-2 h-2 rounded-full bg-gray-500 mr-2"></div>Normal</div>
    }
  }

  return (
    <div className="container max-w-7xl mx-auto py-8 px-4">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{navigationLabel}</h1>
          <p className="text-muted-foreground mt-1">
            Manage all your active and pending {navigationLabel.toLowerCase()}
          </p>
        </div>
        <div className="mt-4 lg:mt-0">
          <Button onClick={() => router.push('/matters/new')}>
            <Plus className="mr-2 h-4 w-4" />
            New {navigationLabel.slice(0, -1)}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total {navigationLabel}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalMatters}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all practice areas
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active {navigationLabel}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeMatters}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Currently in progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Deadlines</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.upcomingDeadlines}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all {navigationLabel.toLowerCase()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">{navigationLabel} Documents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.recentDocuments}</div>
            <p className="text-xs text-muted-foreground mt-1">
              Total {navigationLabel.toLowerCase()}-linked documents
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and filters */}
      <Card className="mb-6">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Search and Filter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder={`Search ${navigationLabel.toLowerCase()} or clients...`}
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
              </div>
            </div>

            <div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  {MATTER_STATUSES.map(status => (
                    <SelectItem key={status.id} value={status.id}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select value={practiceAreaFilter} onValueChange={setPracticeAreaFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Practice Area" />
                </SelectTrigger>
                <SelectContent>
                  {PRACTICE_AREAS.map(area => (
                    <SelectItem key={area.id} value={area.id}>
                      {area.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="md:col-span-3 flex justify-end">
              <Button variant="outline" onClick={handleSearch}>
                <Search className="mr-2 h-4 w-4" />
                Apply Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Matters Table */}
      <Card>
        <CardContent className="pt-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{navigationLabel.slice(0, -1)}</TableHead>
                <TableHead>Client</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Practice Area</TableHead>
                <TableHead>Filed Date</TableHead>
                <TableHead>Next Deadline</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                      Loading {navigationLabel.toLowerCase()}...
                    </div>
                  </TableCell>
                </TableRow>
              ) : matters.length > 0 ? (
                matters.map((matter) => {
                  const daysUntilDeadline = getDaysFromNow(matter.trialDate ?? null)
                  const matterLabel = getMatterDisplayLabel(matter.practiceArea)

                  return (
                    <TableRow
                      key={matter.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => router.push(`/matters/${matter.id}`)}
                    >
                      <TableCell>
                        <div className="flex items-center">
                          <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                          <div>
                            <div className="font-medium">{matter.title}</div>
                            {matter.description && (
                              <div className="text-sm text-muted-foreground truncate max-w-xs">
                                {matter.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                          {matter.clients?.[0]?.fullName || 'No client assigned'}
                        </div>
                      </TableCell>
                      <TableCell>{renderStatusBadge(matter.status)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {matter.practiceArea || 'N/A'}
                        </Badge>
                      </TableCell>
                      <TableCell>{formatDate(matter.createdAt ?? null)}</TableCell>
                      <TableCell>
                        {matter.trialDate ? (
                          <div>
                            <div className="font-medium">Trial Date</div>
                            <div className="text-sm text-muted-foreground flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {formatDate(matter.trialDate ?? null)}
                              {daysUntilDeadline !== null && (
                                <span className={`ml-2 ${daysUntilDeadline <= 7 ? 'text-red-600 font-medium' : ''}`}>
                                  ({daysUntilDeadline > 0 ? `${daysUntilDeadline} days` : 'Today'})
                                </span>
                              )}
                            </div>
                          </div>
                        ) : (
                          <span className="text-muted-foreground">None</span>
                        )}
                      </TableCell>
                      <TableCell>{renderPriority(matter.priorityLevel || 'medium')}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            onClick={(e) => e.stopPropagation()}
                            align="end"
                          >
                            <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}`)}>
                              <Briefcase className="mr-2 h-4 w-4" />
                              View {matterLabel}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}/edit`)}>
                              <FileText className="mr-2 h-4 w-4" />
                              Edit Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}/documents`)}>
                              <FileText className="mr-2 h-4 w-4" />
                              View Documents
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/matters/${matter.id}/deadlines`)}>
                              <Calendar className="mr-2 h-4 w-4" />
                              Manage Deadlines
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => router.push(`/clients/${matter.clients?.[0]?.id}`)}
                              disabled={!matter.clients?.[0]?.id}
                            >
                              <Users className="mr-2 h-4 w-4" />
                              View Client
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  )
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex flex-col items-center">
                      <Briefcase className="h-12 w-12 text-muted-foreground mb-4" />
                      <p className="text-lg font-medium">No {navigationLabel.toLowerCase()} found</p>
                      <p className="text-muted-foreground mb-4">
                        No {navigationLabel.toLowerCase()} match your current search criteria.
                      </p>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setSearchQuery('')
                            setStatusFilter('all')
                            setPracticeAreaFilter('all')
                          }}
                        >
                          Clear filters
                        </Button>
                        <Button onClick={() => router.push('/matters/new')}>
                          <Plus className="mr-2 h-4 w-4" />
                          Create {navigationLabel.slice(0, -1)}
                        </Button>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>

        {/* Pagination */}
        {totalPages > 1 && (
          <CardFooter className="flex justify-center py-4">
            <nav className="mx-auto flex w-full justify-center">
              <ul className="flex flex-row items-center gap-1">
                <li>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(Math.max(1, page - 1))}
                    disabled={page <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                </li>
                <li className="flex items-center gap-1 px-3">
                  <span className="text-sm text-muted-foreground">
                    Page {page} of {totalPages}
                  </span>
                </li>
                <li>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(Math.min(totalPages, page + 1))}
                    disabled={page >= totalPages}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </li>
              </ul>
            </nav>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}
