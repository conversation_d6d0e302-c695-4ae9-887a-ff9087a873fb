'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

// Case details page props
interface CaseDetailsPageProps {
  params: {
    id: string
  }
}

/**
 * Redirect page for legacy /cases/[id] route
 * Redirects users to the new /matters/[id] route
 */
export default function CaseDetailRedirectPage({ params }: CaseDetailsPageProps) {
  const router = useRouter()
  const caseId = params.id

  useEffect(() => {
    // Redirect to matters detail page
    router.replace(`/matters/${caseId}`)
  }, [router, caseId])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Redirecting...</h1>
        <p className="text-muted-foreground">
          Taking you to the matter details page...
        </p>
      </div>
    </div>
  )
}
