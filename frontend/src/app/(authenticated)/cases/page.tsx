'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

/**
 * Redirect page for legacy /cases route
 * Redirects users to the new /matters route
 */
export default function CasesRedirectPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to matters page
    router.replace('/matters')
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-2">Redirecting...</h1>
        <p className="text-muted-foreground">
          Taking you to the matters page...
        </p>
      </div>
    </div>
  )
}
