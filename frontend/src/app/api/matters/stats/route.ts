// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server'
import { SupabaseClient } from '@supabase/supabase-js'
import { withAuth, AuthUser, UserRole } from '@/lib/auth/server-exports'

/**
 * GET /api/matters/stats
 *
 * Retrieves matter statistics for the dashboard
 */
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (req: NextRequest, user: AuthUser, supabase: SupabaseClient) => {
    try {
      // Validate tenantId is defined
      if (!user.tenantId) {
        console.error('tenantId is undefined in JWT claims');
        return NextResponse.json(
          { error: 'Authorization error: Missing tenant information' },
          { status: 400 }
        );
      }

      // First, fetch total and active matter counts
      const { data: mattersData, error: mattersError } = await supabase
        .schema('tenants')
        .from('matters')
        .select('id, status')
        .eq('tenant_id', user.tenantId);

      if (mattersError) {
        console.error('Error fetching matter counts:', mattersError);
        return NextResponse.json(
          { error: 'Failed to fetch matter statistics' },
          { status: 500 }
        );
      }

      // Calculate basic matter stats
      const totalMatters = mattersData.length;
      const activeMatters = mattersData.filter(m => m.status === 'active').length;

      // Get matter IDs for subsequent queries
      const matterIds = mattersData.map(m => m.id);

      // If no matters, return early with zeros
      if (matterIds.length === 0) {
        return NextResponse.json({
          total: 0,
          active: 0,
          upcomingDeadlines: 0,
          recentDocuments: 0
        });
      }

      // Fetch upcoming deadlines count (deadlines in the future, not completed)
      const now = new Date().toISOString();

      let upcomingDeadlines = 0;
      let deadlinesError = null;

      if (matterIds.length > 0) {
        const result = await supabase
          .schema('tenants')
          .from('deadlines')
          .select('id', { count: 'exact', head: true })
          .eq('tenant_id', user.tenantId)
          .in('matter_id', matterIds)
          .gt('due_date', now)
          .eq('completed', false);

        upcomingDeadlines = result.count || 0;
        deadlinesError = result.error;
      }

      if (deadlinesError) {
        console.error('Error fetching deadline count:', deadlinesError);
        // Continue with other stats
      }

      // Fetch document count associated with all matters
      let documentCount = 0;
      let documentsError = null;

      if (matterIds.length > 0) {
        // Check if document_matters table exists, fallback to document_cases for backward compatibility
        try {
          const result = await supabase
            .schema('tenants')
            .from('document_matters')
            .select('document_id', { count: 'exact', head: true })
            .eq('tenant_id', user.tenantId)
            .in('matter_id', matterIds);

          documentCount = result.count || 0;
          documentsError = result.error;
        } catch (docMattersError) {
          // Fallback to document_cases table if document_matters doesn't exist
          try {
            const result = await supabase
              .schema('tenants')
              .from('document_cases')
              .select('document_id', { count: 'exact', head: true })
              .eq('tenant_id', user.tenantId)
              .in('case_id', matterIds);

            documentCount = result.count || 0;
            documentsError = result.error;
          } catch (docCasesError) {
            console.error('Error fetching document count from both tables:', docCasesError);
            documentsError = docCasesError;
          }
        }
      }

      if (documentsError) {
        console.error('Error fetching document count:', documentsError);
        // Continue with other stats
      }

      // Return all statistics
      return NextResponse.json({
        total: totalMatters,
        active: activeMatters,
        upcomingDeadlines: upcomingDeadlines || 0,
        recentDocuments: documentCount || 0
      });

    } catch (error) {
      console.error('Error in matter statistics endpoint:', error);
      return NextResponse.json(
        { error: 'An unexpected error occurred' },
        { status: 500 }
      );
    }
  }
);
