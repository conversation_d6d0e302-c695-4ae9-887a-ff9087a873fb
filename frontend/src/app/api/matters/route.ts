// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server'
import { SupabaseClient } from '@supabase/supabase-js'
import { withAuth, AuthUser, UserRole } from '@/lib/auth/server-exports'

/**
 * GET /api/matters
 *
 * Retrieves matters for the authenticated user's tenant with optional filtering
 */
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (req: NextRequest, user: AuthUser, supabase: SupabaseClient) => {
    try {
      // Validate tenantId is defined
      if (!user.tenantId) {
        console.error('tenantId is undefined in JWT claims');
        return NextResponse.json(
          { error: 'Authorization error: Missing tenant information' },
          { status: 400 }
        );
      }

      // Parse query parameters
      const { searchParams } = new URL(req.url);
      const page = parseInt(searchParams.get('page') || '1');
      const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100);
      const status = searchParams.get('status');
      const practiceArea = searchParams.get('practice_area');
      const clientId = searchParams.get('client_id');
      const search = searchParams.get('search');

      // Build the query
      let query = supabase
        .schema('tenants')
        .from('matters')
        .select(`
          id,
          title,
          description,
          status,
          practice_area,
          display_label,
          sensitive,
          created_at,
          updated_at,
          matter_metadata,
          rejection_reason,
          client_id,
          created_by,
          trial_date,
          priority_level,
          clients:client_id(id, full_name)
        `, { count: 'exact' })
        .eq('tenant_id', user.tenantId);

      // Apply filters
      if (status && status !== 'all') {
        query = query.eq('status', status);
      }

      if (practiceArea && practiceArea !== 'all') {
        query = query.eq('practice_area', practiceArea);
      }

      if (clientId) {
        query = query.eq('client_id', clientId);
      }

      if (search) {
        query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`);
      }

      // Apply pagination
      const from = (page - 1) * limit;
      const to = from + limit - 1;
      query = query.range(from, to);

      // Order by created_at desc
      query = query.order('created_at', { ascending: false });

      const { data: matters, error, count } = await query;

      if (error) {
        console.error('Error fetching matters:', error);
        return NextResponse.json(
          { error: 'Failed to fetch matters' },
          { status: 500 }
        );
      }

      // Transform the data to match expected format
      const transformedMatters = matters?.map(matter => ({
        ...matter,
        clients: matter.clients ? [matter.clients] : [],
        trialDate: matter.trial_date,
        priorityLevel: matter.priority_level,
        practiceArea: matter.practice_area,
        createdAt: matter.created_at,
        updatedAt: matter.updated_at,
        metadata: matter.matter_metadata
      })) || [];

      return NextResponse.json({
        matters: transformedMatters,
        totalCount: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
        currentPage: page
      });

    } catch (error) {
      console.error('Error in matters endpoint:', error);
      return NextResponse.json(
        { error: 'An unexpected error occurred' },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/matters
 *
 * Creates a new matter
 */
export const POST = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (req: NextRequest, user: AuthUser, supabase: SupabaseClient) => {
    try {
      // Validate tenantId is defined
      if (!user.tenantId) {
        console.error('tenantId is undefined in JWT claims');
        return NextResponse.json(
          { error: 'Authorization error: Missing tenant information' },
          { status: 400 }
        );
      }

      const body = await req.json();

      // Create new matter
      const newMatter = {
        ...body,
        tenant_id: user.tenantId,
        created_by: user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        matter_metadata: body.metadata || {}
      };

      const { data: matter, error } = await supabase
        .schema('tenants')
        .from('matters')
        .insert(newMatter)
        .select(`
          id,
          title,
          description,
          status,
          practice_area,
          display_label,
          sensitive,
          created_at,
          updated_at,
          matter_metadata,
          rejection_reason,
          client_id,
          created_by,
          trial_date,
          priority_level,
          clients:client_id(id, full_name)
        `)
        .single();

      if (error) {
        console.error('Error creating matter:', error);
        return NextResponse.json(
          { error: 'Failed to create matter' },
          { status: 500 }
        );
      }

      // Transform the data to match expected format
      const transformedMatter = {
        ...matter,
        clients: matter.clients ? [matter.clients] : [],
        trialDate: matter.trial_date,
        priorityLevel: matter.priority_level,
        practiceArea: matter.practice_area,
        createdAt: matter.created_at,
        updatedAt: matter.updated_at,
        metadata: matter.matter_metadata
      };

      return NextResponse.json(transformedMatter, { status: 201 });

    } catch (error) {
      console.error('Error in matters POST endpoint:', error);
      return NextResponse.json(
        { error: 'An unexpected error occurred' },
        { status: 500 }
      );
    }
  }
);
