// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server'
import { SupabaseClient } from '@supabase/supabase-js'
import { withAuth, AuthUser, UserRole } from '@/lib/auth/server-exports'

/**
 * GET /api/matters-test
 *
 * Test endpoint for matters - returns a simple response to verify API connectivity
 */
export const GET = withAuth(
  [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff],
  async (req: NextRequest, user: AuthUser, supabase: SupabaseClient) => {
    try {
      // Validate tenantId is defined
      if (!user.tenantId) {
        console.error('tenantId is undefined in JWT claims');
        return NextResponse.json(
          { error: 'Authorization error: Missing tenant information' },
          { status: 400 }
        );
      }

      // Simple query to test connectivity and get basic matters data
      const { data: matters, error } = await supabase
        .schema('tenants')
        .from('matters')
        .select(`
          id,
          title,
          description,
          status,
          practice_area,
          display_label,
          sensitive,
          created_at,
          updated_at,
          matter_metadata,
          rejection_reason,
          client_id,
          created_by,
          trial_date,
          priority_level,
          clients:client_id(id, full_name)
        `)
        .eq('tenant_id', user.tenantId)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) {
        console.error('Error in matters-test endpoint:', error);
        return NextResponse.json(
          { 
            success: false, 
            error: 'Failed to fetch matters',
            details: error.message 
          },
          { status: 500 }
        );
      }

      // Transform the data to match expected format
      const transformedMatters = matters?.map(matter => ({
        ...matter,
        clients: matter.clients ? [matter.clients] : [],
        trialDate: matter.trial_date,
        priorityLevel: matter.priority_level,
        practiceArea: matter.practice_area,
        createdAt: matter.created_at,
        updatedAt: matter.updated_at,
        metadata: matter.matter_metadata
      })) || [];

      return NextResponse.json({
        success: true,
        data: transformedMatters,
        count: transformedMatters.length,
        message: 'Matters test endpoint working correctly'
      });

    } catch (error) {
      console.error('Error in matters-test endpoint:', error);
      return NextResponse.json(
        { 
          success: false, 
          error: 'An unexpected error occurred',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      );
    }
  }
);
