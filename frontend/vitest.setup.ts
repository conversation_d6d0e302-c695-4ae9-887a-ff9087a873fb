import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect method with methods from react-testing-library
expect.extend(matchers);

// Run cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

// Mock ResizeObserver for Radix UI components
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Global Supabase mock setup for all tests
vi.mock('@supabase/supabase-js', () => {
  // Create chainable mock functions
  const createChainableMock = () => {
    const mock = vi.fn();
    mock.mockReturnValue(mock);
    return mock;
  };

  // Create a comprehensive mock for query builders with shared state
  const sharedQueryBuilder = {
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    upsert: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    neq: vi.fn().mockReturnThis(),
    gt: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lt: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    like: vi.fn().mockReturnThis(),
    ilike: vi.fn().mockReturnThis(),
    is: vi.fn().mockReturnThis(),
    in: vi.fn().mockReturnThis(),
    contains: vi.fn().mockReturnThis(),
    containedBy: vi.fn().mockReturnThis(),
    rangeGt: vi.fn().mockReturnThis(),
    rangeGte: vi.fn().mockReturnThis(),
    rangeLt: vi.fn().mockReturnThis(),
    rangeLte: vi.fn().mockReturnThis(),
    rangeAdjacent: vi.fn().mockReturnThis(),
    overlaps: vi.fn().mockReturnThis(),
    textSearch: vi.fn().mockReturnThis(),
    match: vi.fn().mockReturnThis(),
    not: vi.fn().mockReturnThis(),
    or: vi.fn().mockReturnThis(),
    filter: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
    abortSignal: vi.fn().mockReturnThis(),
    single: vi.fn().mockResolvedValue({
      data: {
        id: '1',
        read: true,
        updated_at: '2023-01-01T00:00:00Z',
        subscription_plans: {
          name: 'Test Plan',
          code: 'test_plan',
          base_price_monthly: 29.99,
          base_price_yearly: 299.99
        },
        subscription_addons: {
          name: 'Test Addon',
          code: 'test_addon',
          category: 'feature'
        }
      },
      error: null
    }),
    maybeSingle: vi.fn().mockResolvedValue({
      data: {
        id: '1',
        read: true,
        updated_at: '2023-01-01T00:00:00Z',
        subscription_plans: {
          name: 'Test Plan',
          code: 'test_plan',
          base_price_monthly: 29.99,
          base_price_yearly: 299.99
        },
        subscription_addons: {
          name: 'Test Addon',
          code: 'test_addon',
          category: 'feature'
        }
      },
      error: null
    }),
    csv: vi.fn().mockResolvedValue({ data: '', error: null }),
    geojson: vi.fn().mockResolvedValue({ data: null, error: null }),
    explain: vi.fn().mockResolvedValue({ data: null, error: null }),
    rollback: vi.fn().mockResolvedValue({ data: null, error: null }),
    returns: vi.fn().mockReturnThis(),
    // Make the builder itself thenable for direct await
    then: vi.fn((resolve) => resolve({
      data: [
        {
          id: '1',
          subscription_addons: {
            name: 'Test Addon',
            code: 'test_addon',
            category: 'feature'
          }
        },
        {
          id: '2',
          subscription_addons: {
            name: 'Another Addon',
            code: 'another_addon',
            category: 'feature'
          }
        }
      ],
      error: null,
      count: 2
    })),
    catch: vi.fn(),
    finally: vi.fn(),
  };

  const createQueryBuilder = () => sharedQueryBuilder;

  // Create schema mock
  const createSchemaMock = () => ({
    from: vi.fn(() => createQueryBuilder()),
  });

  // Create main client mock
  const createClientMock = () => {
    const queryBuilder = createQueryBuilder();

    // Configure insert chain for trackResourceUsage
    queryBuilder.insert.mockImplementation(() => ({
      select: vi.fn().mockReturnValue({
        single: vi.fn().mockResolvedValue({
          data: {
            id: 'mock-usage-id',
            tenant_id: 'mock-tenant',
            usage_type: 'mock-type',
            usage_count: 1,
            resource_size_bytes: 1024,
            period_start: new Date().toISOString(),
            period_end: new Date().toISOString(),
            created_at: new Date().toISOString(),
          },
          error: null
        })
      })
    }));

    return {
      from: vi.fn(() => queryBuilder),
      schema: vi.fn(() => ({
        from: vi.fn(() => queryBuilder)
      })),
      rpc: vi.fn().mockResolvedValue({ data: null, error: null }),
      storage: {
      from: vi.fn().mockReturnThis(),
      upload: vi.fn().mockResolvedValue({ data: null, error: null }),
      download: vi.fn().mockResolvedValue({ data: null, error: null }),
      list: vi.fn().mockResolvedValue({ data: [], error: null }),
      remove: vi.fn().mockResolvedValue({ data: null, error: null }),
      createSignedUrl: vi.fn().mockResolvedValue({ data: null, error: null }),
      createSignedUrls: vi.fn().mockResolvedValue({ data: [], error: null }),
      getPublicUrl: vi.fn().mockReturnValue({ data: { publicUrl: 'mock-url' } }),
    },
    auth: {
      signUp: vi.fn().mockResolvedValue({ data: null, error: null }),
      signInWithPassword: vi.fn().mockResolvedValue({ data: null, error: null }),
      signInWithOAuth: vi.fn().mockResolvedValue({ data: null, error: null }),
      signOut: vi.fn().mockResolvedValue({ error: null }),
      getSession: vi.fn().mockResolvedValue({ data: { session: null }, error: null }),
      getUser: vi.fn().mockResolvedValue({ data: { user: null }, error: null }),
      updateUser: vi.fn().mockResolvedValue({ data: null, error: null }),
      setSession: vi.fn().mockResolvedValue({ data: null, error: null }),
      refreshSession: vi.fn().mockResolvedValue({ data: null, error: null }),
      resetPasswordForEmail: vi.fn().mockResolvedValue({ data: null, error: null }),
      onAuthStateChange: vi.fn().mockReturnValue({ data: { subscription: { unsubscribe: vi.fn() } } }),
    },
    realtime: {
      channel: vi.fn().mockReturnValue({
        on: vi.fn().mockReturnThis(),
        subscribe: vi.fn().mockReturnThis(),
        unsubscribe: vi.fn().mockReturnThis(),
      }),
      removeChannel: vi.fn(),
      removeAllChannels: vi.fn(),
      getChannels: vi.fn().mockReturnValue([]),
    },
  };
  };

  return {
    createClient: vi.fn(() => createClientMock()),
  };
});
