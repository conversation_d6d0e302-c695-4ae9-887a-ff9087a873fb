import { NotificationService, NotificationType, NotificationSeverity } from '@/lib/services/notification-service';
import { createClient } from '@supabase/supabase-js';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';

describe('NotificationService', () => {
  let supabase: any;
  let notificationService: NotificationService;

  beforeEach(() => {
    // Use fake timers and set a fixed system time
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2023-01-15T12:00:00Z'));

    // Use the global mock from vitest.setup.ts
    supabase = createClient('https://example.com', 'fake-key');
    notificationService = new NotificationService(supabase);
  });

  afterEach(() => {
    // Restore real timers
    vi.useRealTimers();
  });

  describe('createNotification', () => {
    it('should call database methods without throwing schema errors', async () => {
      // This test verifies that the service can call the database methods
      // without throwing "client.schema is not a function" errors

      // Call the method - should not throw schema errors
      const result = await notificationService.createNotification(
        'tenant-1',
        'user-1',
        'subscription_trial_ending',
        'Your trial is ending soon',
        'medium',
        { daysRemaining: 3 }
      );

      // Verify that we get a result (null is acceptable for the mock)
      expect(result).toBeDefined();
    });

    it('should handle errors gracefully', async () => {
      // This test verifies that the service can handle errors without schema issues

      // Call the method - should not throw schema errors
      const result = await notificationService.createNotification(
        'tenant-1',
        'user-1',
        'subscription_trial_ending',
        'Your trial is ending soon'
      );

      // Verify that we get a result (null is acceptable for the mock)
      expect(result).toBeDefined();
    });
  });

  describe('getUserNotifications', () => {
    it('should call database methods without throwing schema errors', async () => {
      // This test verifies that the service can call the database methods
      // without throwing "client.schema is not a function" errors

      // Call the method - should not throw schema errors
      const result = await notificationService.getUserNotifications('user-1');

      // Verify that we get a result (empty array is acceptable for the mock)
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    it('should handle includeRead parameter without schema errors', async () => {
      // Call the method with includeRead parameter - should not throw schema errors
      const result = await notificationService.getUserNotifications('user-1', true);

      // Verify that we get a result
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    it('should handle limit parameter without schema errors', async () => {
      // Call the method with a custom limit - should not throw schema errors
      const result = await notificationService.getUserNotifications('user-1', false, 10);

      // Verify that we get a result
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });

    it('should handle errors gracefully', async () => {
      // Call the method - should not throw schema errors
      const result = await notificationService.getUserNotifications('user-1');

      // Verify that we get a result
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('markAsRead', () => {
    it('should call database methods without throwing schema errors', async () => {
      // This test verifies that the service can call the database methods
      // without throwing "client.schema is not a function" errors

      // Call the method - should not throw schema errors
      const result = await notificationService.markAsRead('1', 'user-1');

      // Verify that we get a result (null is acceptable for the mock)
      expect(result).toBeDefined();
    });

    it('should handle errors gracefully', async () => {
      // Call the method - should not throw schema errors
      const result = await notificationService.markAsRead('1', 'user-1');

      // Verify that we get a result
      expect(result).toBeDefined();
    });
  });

  describe('markAllAsRead', () => {
    it('should call database methods without throwing schema errors', async () => {
      // This test verifies that the service can call the database methods
      // without throwing "client.schema is not a function" errors

      // Call the method - should not throw schema errors
      const result = await notificationService.markAllAsRead('user-1');

      // Verify that the method completes without error (returns void)
      expect(result).toBeUndefined();
    });

    it('should handle errors gracefully', async () => {
      // Call the method - should not throw schema errors
      const result = await notificationService.markAllAsRead('user-1');

      // Verify that the method completes without error (returns void)
      expect(result).toBeUndefined();
    });
  });

  describe('getUnreadCount', () => {
    it('should call database methods without throwing schema errors', async () => {
      // This test verifies that the service can call the database methods
      // without throwing "client.schema is not a function" errors

      // Call the method - should not throw schema errors
      const result = await notificationService.getUnreadCount('user-1');

      // Verify that we get a result (0 is acceptable for the mock)
      expect(result).toBeDefined();
      expect(typeof result).toBe('number');
    });

    it('should handle errors gracefully', async () => {
      // Call the method - should not throw schema errors
      const result = await notificationService.getUnreadCount('user-1');

      // Verify that we get a result
      expect(result).toBeDefined();
      expect(typeof result).toBe('number');
    });
  });

  describe('createTrialEndingNotification', () => {
    it('should call createNotification with correct args for trial ending (3 days)', async () => {
      const createNotificationSpy = vi.spyOn(notificationService, 'createNotification');
      createNotificationSpy.mockResolvedValue({ id: '1' } as any);

      const tenantId = 'tenant-1';
      const userId = 'user-1';
      const daysRemaining = 3;
      const expectedType = 'subscription_trial_ending';
      const expectedMessage = `Your trial will end in ${daysRemaining} days. Please update your payment information to continue using the service.`;
      const expectedSeverity = 'medium';
      const expectedContext = { daysRemaining };

      await notificationService.createNotification(tenantId, userId, expectedType, expectedMessage, expectedSeverity, expectedContext);

      expect(createNotificationSpy).toHaveBeenCalledWith(
        tenantId,
        userId,
        expectedType,
        expectedMessage,
        expectedSeverity,
        expectedContext
      );
    });

    it('should call createNotification with high severity for 1 day remaining', async () => {
      const createNotificationSpy = vi.spyOn(notificationService, 'createNotification');
      createNotificationSpy.mockResolvedValue({ id: '1' } as any);

      const tenantId = 'tenant-1';
      const userId = 'user-1';
      const daysRemaining = 1;
      const expectedType = 'subscription_trial_ending';
      const expectedMessage = `Your trial will end in ${daysRemaining} day. Please update your payment information to continue using the service.`; // Singular 'day'
      const expectedSeverity = 'high'; // Severity changes
      const expectedContext = { daysRemaining };

      await notificationService.createNotification(tenantId, userId, expectedType, expectedMessage, expectedSeverity, expectedContext);

      expect(createNotificationSpy).toHaveBeenCalledWith(
        tenantId,
        userId,
        expectedType,
        expectedMessage,
        expectedSeverity,
        expectedContext
      );
    });
  });

  describe('createQuotaApproachingNotification', () => {
    it('should call createNotification with correct args for quota approaching (80%)', async () => {
      const createNotificationSpy = vi.spyOn(notificationService, 'createNotification');
      createNotificationSpy.mockResolvedValue({ id: '1' } as any);

      const tenantId = 'tenant-1';
      const userId = 'user-1';
      const quotaType = 'document uploads';
      const percentUsed = 80;
      const expectedType = 'quota_limit_approaching';
      const expectedMessage = `You have used ${percentUsed}% of your ${quotaType} quota for this period.`;
      const expectedSeverity = 'medium';
      const expectedContext = { quotaType, percentUsed };

      await notificationService.createNotification(tenantId, userId, expectedType, expectedMessage, expectedSeverity, expectedContext);

      expect(createNotificationSpy).toHaveBeenCalledWith(
        tenantId,
        userId,
        expectedType,
        expectedMessage,
        expectedSeverity,
        expectedContext
      );
    });

    it('should call createNotification with high severity for 90% or more usage', async () => {
      const createNotificationSpy = vi.spyOn(notificationService, 'createNotification');
      createNotificationSpy.mockResolvedValue({ id: '1' } as any);

      const tenantId = 'tenant-1';
      const userId = 'user-1';
      const quotaType = 'case creations';
      const percentUsed = 95;
      const expectedType = 'quota_limit_approaching';
      const expectedMessage = `You have used ${percentUsed}% of your ${quotaType} quota for this period.`;
      const expectedSeverity = 'high'; // Severity changes
      const expectedContext = { quotaType, percentUsed };

      await notificationService.createNotification(tenantId, userId, expectedType, expectedMessage, expectedSeverity, expectedContext);

      expect(createNotificationSpy).toHaveBeenCalledWith(
        tenantId,
        userId,
        expectedType,
        expectedMessage,
        expectedSeverity,
        expectedContext
      );
    });
  });

  describe('createQuotaReachedNotification', () => {
    it('should call createNotification with correct args for quota reached', async () => {
      const createNotificationSpy = vi.spyOn(notificationService, 'createNotification');
      createNotificationSpy.mockResolvedValue({ id: '1' } as any);

      const tenantId = 'tenant-1';
      const userId = 'user-1';
      const quotaType = 'API calls';
      const expectedType = 'quota_limit_reached';
      const expectedMessage = `You have reached your ${quotaType} quota for this period. Please upgrade your plan to continue.`;
      const expectedSeverity = 'high';
      const expectedContext = { quotaType };

      await notificationService.createNotification(tenantId, userId, expectedType, expectedMessage, expectedSeverity, expectedContext);

      expect(createNotificationSpy).toHaveBeenCalledWith(
        tenantId,
        userId,
        expectedType,
        expectedMessage,
        expectedSeverity,
        expectedContext
      );
    });
  });
});
