/**
 * Integration tests for matter list component dynamic labeling
 * Tests that matter list views display correct terminology based on practice area
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { PracticeArea, MatterStatus, type Matter, getMatterDisplayLabel } from '@/types/domain/tenants/Matter'

// Mock the useMattersApi hook
const mockGetAllMatters = vi.fn()
const mockGetMatterStats = vi.fn()
vi.mock('@/hooks/useMattersApi', () => ({
  useMattersApi: () => ({
    getAllMatters: mockGetAllMatters,
    getMatterStats: mockGetMatterStats,
    isLoading: false,
    error: null
  })
}))

// Mock Next.js router
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  usePathname: () => '/cases',
  useRouter: () => ({
    push: mockPush,
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  })
}))

// Mock authentication
vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'user-1', email: '<EMAIL>' },
    isLoading: false,
    signOut: vi.fn()
  })
}))

// Mock authenticated fetch
vi.mock('@/hooks/useAuthenticatedFetch', () => ({
  useAuthenticatedFetch: () => vi.fn()
}))

// Mock Supabase
vi.mock('@/lib/supabase/provider', () => ({
  useSupabase: () => ({
    client: {},
    user: { id: 'user-1' }
  })
}))

// Mock toast
vi.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}))

describe('Matter List Component Dynamic Labeling', () => {
  const createMockMatters = (practiceAreas: PracticeArea[]): Matter[] => {
    return practiceAreas.map((practiceArea, index) => ({
      id: `matter-${index + 1}`,
      tenantId: 'tenant-1',
      title: `Test ${practiceArea} ${index + 1}`,
      description: 'Test description',
      status: MatterStatus.ACTIVE,
      practiceArea,
      displayLabel: getMatterDisplayLabel(practiceArea),
      sensitive: false,
      caseNumber: practiceArea === PracticeArea.LITIGATION ? `CV-2023-${index + 1}` : null,
      courtName: practiceArea === PracticeArea.LITIGATION ? 'Superior Court' : null,
      jurisdiction: practiceArea === PracticeArea.LITIGATION ? 'California' : null,
      filingDate: practiceArea === PracticeArea.LITIGATION ? '2023-01-15T00:00:00Z' : null,
      trialDate: practiceArea === PracticeArea.LITIGATION ? '2023-06-15T00:00:00Z' : null,
      primaryAttorneyId: 'attorney-1',
      priorityLevel: 'medium',
      statueOfLimitations: practiceArea === PracticeArea.LITIGATION ? '2025-01-15T00:00:00Z' : null,
      rejectionReason: null,
      createdBy: 'user-1',
      createdAt: '2023-01-01T00:00:00Z',
      updatedBy: null,
      updatedAt: null,
      clients: [{ id: 'client-1', fullName: 'John Doe', email: '<EMAIL>' }]
    }))
  }

  const mockStats = {
    total: 5,
    active: 3,
    upcomingDeadlines: 2,
    recentDocuments: 10
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockGetMatterStats.mockResolvedValue(mockStats)
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Cases Page (Litigation-focused)', () => {
    let CasesPage: any

    beforeEach(async () => {
      const module = await import('@/app/(authenticated)/cases/page')
      CasesPage = module.default
    })

    it('should display "Cases" title when all matters are litigation', async () => {
      const litigationMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.LITIGATION
      ])
      
      mockGetAllMatters.mockResolvedValue(litigationMatters)

      render(<CasesPage />)

      // Wait for the page to load and determine navigation label
      await waitFor(() => {
        expect(screen.getByText('Cases')).toBeInTheDocument()
      }, { timeout: 3000 })

      // Check that the description uses "cases"
      await waitFor(() => {
        expect(screen.getByText(/Manage all your active and pending cases/)).toBeInTheDocument()
      })
    })

    it('should display "Matters" title when practice areas are mixed', async () => {
      const mixedMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.TRANSACTION
      ])
      
      mockGetAllMatters.mockResolvedValue(mixedMatters)

      render(<CasesPage />)

      // Wait for the page to load and determine navigation label
      await waitFor(() => {
        expect(screen.getByText('Matters')).toBeInTheDocument()
      }, { timeout: 3000 })

      // Check that the description uses "matters"
      await waitFor(() => {
        expect(screen.getByText(/Manage all your active and pending matters/)).toBeInTheDocument()
      })
    })

    it('should display correct dropdown labels for individual matters', async () => {
      const mixedMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.TRANSACTION
      ])
      
      mockGetAllMatters.mockResolvedValue(mixedMatters)

      render(<CasesPage />)

      // Wait for matters to load
      await waitFor(() => {
        expect(screen.getByText('Test litigation 1')).toBeInTheDocument()
      }, { timeout: 3000 })

      // The dropdown should show "View Case" for litigation and "View Matter" for transaction
      // Note: These are in dropdown menus that may not be visible until clicked
      // But the logic is tested in the component
    })
  })

  describe('Matter Display Label Logic', () => {
    it('should return correct labels for different practice areas', () => {
      const testCases = [
        { practiceArea: PracticeArea.LITIGATION, expectedLabel: 'Case' },
        { practiceArea: PracticeArea.TRANSACTION, expectedLabel: 'Matter' },
        { practiceArea: PracticeArea.ADVISORY, expectedLabel: 'Matter' },
        { practiceArea: PracticeArea.IP, expectedLabel: 'Matter' }
      ]

      testCases.forEach(({ practiceArea, expectedLabel }) => {
        const label = getMatterDisplayLabel(practiceArea)
        expect(label).toBe(expectedLabel)
      })
    })

    it('should create matters with correct display labels', () => {
      const litigationMatter = createMockMatters([PracticeArea.LITIGATION])[0]
      const transactionMatter = createMockMatters([PracticeArea.TRANSACTION])[0]

      expect(litigationMatter.displayLabel).toBe('Case')
      expect(transactionMatter.displayLabel).toBe('Matter')
    })
  })

  describe('Navigation Label Determination Logic', () => {
    it('should determine "Cases" for all-litigation matters', () => {
      const litigationMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.LITIGATION,
        PracticeArea.LITIGATION
      ])

      const allLitigation = litigationMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)
      const navigationLabel = allLitigation ? 'Cases' : 'Matters'

      expect(navigationLabel).toBe('Cases')
    })

    it('should determine "Matters" for mixed practice areas', () => {
      const mixedMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.TRANSACTION,
        PracticeArea.ADVISORY
      ])

      const allLitigation = mixedMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)
      const navigationLabel = allLitigation ? 'Cases' : 'Matters'

      expect(navigationLabel).toBe('Matters')
    })

    it('should default to "Cases" for empty matter list', () => {
      const emptyMatters: Matter[] = []
      
      // Simulate the logic from the component
      const navigationLabel = emptyMatters.length === 0 ? 'Cases' : 
        (emptyMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION) ? 'Cases' : 'Matters')

      expect(navigationLabel).toBe('Cases')
    })
  })

  describe('Loading States', () => {
    it('should show loading message with correct terminology', async () => {
      const litigationMatters = createMockMatters([PracticeArea.LITIGATION])
      
      // Mock a delayed response to test loading state
      mockGetAllMatters.mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve(litigationMatters), 100))
      )

      const CasesPage = (await import('@/app/(authenticated)/cases/page')).default
      render(<CasesPage />)

      // Should show loading with default "cases" terminology initially
      await waitFor(() => {
        expect(screen.getByText(/Loading cases/)).toBeInTheDocument()
      })
    })
  })
})
