/**
 * Integration tests for dynamic labeling functionality
 * Tests the Matter vs Case labeling system across the application
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import {
  getMatterDisplayLabel,
  isLitigationMatter,
  PracticeArea,
  MatterStatus,
  type Matter
} from '@/types/domain/tenants/Matter'

describe('Dynamic Labeling System', () => {
  describe('getMatterDisplayLabel', () => {
    it('should return "Case" for litigation practice area', () => {
      const result = getMatterDisplayLabel(PracticeArea.LITIGATION)
      expect(result).toBe('Case')
    })

    it('should return "Matter" for transaction practice area', () => {
      const result = getMatterDisplayLabel(PracticeArea.TRANSACTION)
      expect(result).toBe('Matter')
    })

    it('should return "Matter" for advisory practice area', () => {
      const result = getMatterDisplayLabel(PracticeArea.ADVISORY)
      expect(result).toBe('Matter')
    })

    it('should return "Matter" for IP practice area', () => {
      const result = getMatterDisplayLabel(PracticeArea.IP)
      expect(result).toBe('Matter')
    })
  })

  describe('isLitigationMatter', () => {
    const createMockMatter = (practiceArea: PracticeArea): Matter => ({
      id: '1',
      tenantId: 'tenant-1',
      title: 'Test Matter',
      description: 'Test description',
      status: MatterStatus.ACTIVE,
      practiceArea,
      displayLabel: getMatterDisplayLabel(practiceArea),
      sensitive: false,
      caseNumber: null,
      courtName: null,
      jurisdiction: null,
      filingDate: null,
      trialDate: null,
      primaryAttorneyId: null,
      priorityLevel: null,
      statueOfLimitations: null,
      rejectionReason: null,
      createdBy: 'user-1',
      createdAt: '2023-01-01T00:00:00Z',
      updatedBy: null,
      updatedAt: null
    })

    it('should return true for litigation matters', () => {
      const matter = createMockMatter(PracticeArea.LITIGATION)
      expect(isLitigationMatter(matter)).toBe(true)
    })

    it('should return false for transaction matters', () => {
      const matter = createMockMatter(PracticeArea.TRANSACTION)
      expect(isLitigationMatter(matter)).toBe(false)
    })

    it('should return false for advisory matters', () => {
      const matter = createMockMatter(PracticeArea.ADVISORY)
      expect(isLitigationMatter(matter)).toBe(false)
    })

    it('should return false for IP matters', () => {
      const matter = createMockMatter(PracticeArea.IP)
      expect(isLitigationMatter(matter)).toBe(false)
    })
  })

  describe('Matter displayLabel property', () => {
    it('should have correct displayLabel for litigation matter', () => {
      const matter: Matter = {
        id: '1',
        tenantId: 'tenant-1',
        title: 'Personal Injury Case',
        description: 'Car accident case',
        status: MatterStatus.ACTIVE,
        practiceArea: PracticeArea.LITIGATION,
        displayLabel: getMatterDisplayLabel(PracticeArea.LITIGATION),
        sensitive: false,
        caseNumber: 'CV-2023-001',
        courtName: 'Superior Court',
        jurisdiction: 'California',
        filingDate: '2023-01-15T00:00:00Z',
        trialDate: '2023-06-15T00:00:00Z',
        primaryAttorneyId: 'attorney-1',
        priorityLevel: 'high',
        statueOfLimitations: '2025-01-15T00:00:00Z',
        rejectionReason: null,
        createdBy: 'user-1',
        createdAt: '2023-01-01T00:00:00Z',
        updatedBy: null,
        updatedAt: null
      }

      expect(matter.displayLabel).toBe('Case')
      expect(isLitigationMatter(matter)).toBe(true)
    })

    it('should have correct displayLabel for transaction matter', () => {
      const matter: Matter = {
        id: '2',
        tenantId: 'tenant-1',
        title: 'Real Estate Transaction',
        description: 'Commercial property purchase',
        status: MatterStatus.ACTIVE,
        practiceArea: PracticeArea.TRANSACTION,
        displayLabel: getMatterDisplayLabel(PracticeArea.TRANSACTION),
        sensitive: false,
        caseNumber: null,
        courtName: null,
        jurisdiction: null,
        filingDate: null,
        trialDate: null,
        primaryAttorneyId: 'attorney-2',
        priorityLevel: 'medium',
        statueOfLimitations: null,
        rejectionReason: null,
        createdBy: 'user-1',
        createdAt: '2023-01-01T00:00:00Z',
        updatedBy: null,
        updatedAt: null
      }

      expect(matter.displayLabel).toBe('Matter')
      expect(isLitigationMatter(matter)).toBe(false)
    })
  })

  describe('Practice Area Consistency', () => {
    it('should maintain consistent labeling across all practice areas', () => {
      const practiceAreas = [
        PracticeArea.LITIGATION,
        PracticeArea.TRANSACTION,
        PracticeArea.ADVISORY,
        PracticeArea.IP
      ]

      practiceAreas.forEach(practiceArea => {
        const label = getMatterDisplayLabel(practiceArea)

        if (practiceArea === PracticeArea.LITIGATION) {
          expect(label).toBe('Case')
        } else {
          expect(label).toBe('Matter')
        }
      })
    })
  })

  describe('Navigation Label Logic', () => {
    const createMockMatters = (practiceAreas: PracticeArea[]): Matter[] => {
      return practiceAreas.map((practiceArea, index) => ({
        id: `matter-${index + 1}`,
        tenantId: 'tenant-1',
        title: `Test ${getMatterDisplayLabel(practiceArea)} ${index + 1}`,
        description: 'Test description',
        status: MatterStatus.ACTIVE,
        practiceArea,
        displayLabel: getMatterDisplayLabel(practiceArea),
        sensitive: false,
        caseNumber: practiceArea === PracticeArea.LITIGATION ? `CV-2023-${index + 1}` : null,
        courtName: practiceArea === PracticeArea.LITIGATION ? 'Superior Court' : null,
        jurisdiction: practiceArea === PracticeArea.LITIGATION ? 'California' : null,
        filingDate: practiceArea === PracticeArea.LITIGATION ? '2023-01-15T00:00:00Z' : null,
        trialDate: practiceArea === PracticeArea.LITIGATION ? '2023-06-15T00:00:00Z' : null,
        primaryAttorneyId: 'attorney-1',
        priorityLevel: 'medium',
        statueOfLimitations: practiceArea === PracticeArea.LITIGATION ? '2025-01-15T00:00:00Z' : null,
        rejectionReason: null,
        createdBy: 'user-1',
        createdAt: '2023-01-01T00:00:00Z',
        updatedBy: null,
        updatedAt: null
      }))
    }

    it('should return "Cases" when all matters are litigation', () => {
      const litigationMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.LITIGATION,
        PracticeArea.LITIGATION
      ])

      // Simulate navigation label logic
      const allLitigation = litigationMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)
      const navigationLabel = allLitigation ? 'Cases' : 'Matters'

      expect(navigationLabel).toBe('Cases')
      expect(allLitigation).toBe(true)
    })

    it('should return "Matters" when practice areas are mixed', () => {
      const mixedMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.TRANSACTION,
        PracticeArea.ADVISORY
      ])

      // Simulate navigation label logic
      const allLitigation = mixedMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)
      const navigationLabel = allLitigation ? 'Cases' : 'Matters'

      expect(navigationLabel).toBe('Matters')
      expect(allLitigation).toBe(false)
    })

    it('should return "Cases" for empty matters array (default)', () => {
      const emptyMatters: Matter[] = []

      // Simulate navigation label logic (default behavior)
      const navigationLabel = emptyMatters.length === 0 ? 'Cases' :
        (emptyMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION) ? 'Cases' : 'Matters')

      expect(navigationLabel).toBe('Cases')
    })

    it('should return "Matters" when only non-litigation matters exist', () => {
      const nonLitigationMatters = createMockMatters([
        PracticeArea.TRANSACTION,
        PracticeArea.ADVISORY,
        PracticeArea.IP
      ])

      // Simulate navigation label logic
      const allLitigation = nonLitigationMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)
      const navigationLabel = allLitigation ? 'Cases' : 'Matters'

      expect(navigationLabel).toBe('Matters')
      expect(allLitigation).toBe(false)
    })
  })
})
