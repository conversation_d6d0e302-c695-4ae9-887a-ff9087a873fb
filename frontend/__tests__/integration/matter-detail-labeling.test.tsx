/**
 * Integration tests for matter detail component dynamic labeling
 * Tests that matter detail pages display correct terminology in headers, forms, and action buttons
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { PracticeArea, MatterStatus, type Matter, getMatterDisplayLabel } from '@/types/domain/tenants/Matter'
import { MatterOverview } from '@/components/matters/matter-overview'

// Mock Next.js router
const mockPush = vi.fn()
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  })
}))

describe('Matter Detail Component Dynamic Labeling', () => {
  const createMockMatter = (practiceArea: PracticeArea): Matter => ({
    id: 'matter-1',
    tenantId: 'tenant-1',
    title: `Test ${practiceArea} Matter`,
    description: 'This is a detailed description of the matter for testing purposes.',
    status: MatterStatus.ACTIVE,
    practiceArea,
    displayLabel: getMatterDisplayLabel(practiceArea),
    sensitive: false,
    caseNumber: practiceArea === PracticeArea.LITIGATION ? 'CV-2023-001' : null,
    courtName: practiceArea === PracticeArea.LITIGATION ? 'Superior Court of California' : null,
    jurisdiction: practiceArea === PracticeArea.LITIGATION ? 'California' : null,
    filingDate: practiceArea === PracticeArea.LITIGATION ? '2023-01-15T00:00:00Z' : null,
    trialDate: practiceArea === PracticeArea.LITIGATION ? '2023-06-15T00:00:00Z' : null,
    primaryAttorneyId: 'attorney-1',
    priorityLevel: 'high',
    statueOfLimitations: practiceArea === PracticeArea.LITIGATION ? '2025-01-15T00:00:00Z' : null,
    rejectionReason: null,
    createdBy: 'user-1',
    createdAt: '2023-01-01T00:00:00Z',
    updatedBy: null,
    updatedAt: null,
    clients: [{ id: 'client-1', fullName: 'John Doe', email: '<EMAIL>' }],
    documentCount: 5,
    deadlineCount: 3,
    noteCount: 2
  })

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('MatterOverview Component', () => {
    it('should display "Case" terminology for litigation matters', () => {
      const litigationMatter = createMockMatter(PracticeArea.LITIGATION)
      
      render(<MatterOverview matterData={litigationMatter} />)

      // Check that the edit button uses "Case"
      expect(screen.getByText('Edit Case')).toBeInTheDocument()

      // Check that the description section uses "Case"
      expect(screen.getByText('Case Description')).toBeInTheDocument()

      // Check that the summary section uses "Case"
      expect(screen.getByText('Case Summary')).toBeInTheDocument()

      // Verify litigation-specific fields are shown
      expect(screen.getByText('Litigation Details')).toBeInTheDocument()
      expect(screen.getByText('Case Number')).toBeInTheDocument()
      expect(screen.getByText('CV-2023-001')).toBeInTheDocument()
      expect(screen.getByText('Court')).toBeInTheDocument()
      expect(screen.getByText('Superior Court of California')).toBeInTheDocument()
    })

    it('should display "Matter" terminology for transaction matters', () => {
      const transactionMatter = createMockMatter(PracticeArea.TRANSACTION)
      
      render(<MatterOverview matterData={transactionMatter} />)

      // Check that the edit button uses "Matter"
      expect(screen.getByText('Edit Matter')).toBeInTheDocument()

      // Check that the description section uses "Matter"
      expect(screen.getByText('Matter Description')).toBeInTheDocument()

      // Check that the summary section uses "Matter"
      expect(screen.getByText('Matter Summary')).toBeInTheDocument()

      // Verify litigation-specific fields are NOT shown
      expect(screen.queryByText('Litigation Details')).not.toBeInTheDocument()
      expect(screen.queryByText('Case Number')).not.toBeInTheDocument()
      expect(screen.queryByText('Court')).not.toBeInTheDocument()
    })

    it('should display "Matter" terminology for advisory matters', () => {
      const advisoryMatter = createMockMatter(PracticeArea.ADVISORY)
      
      render(<MatterOverview matterData={advisoryMatter} />)

      // Check that the edit button uses "Matter"
      expect(screen.getByText('Edit Matter')).toBeInTheDocument()

      // Check that the description section uses "Matter"
      expect(screen.getByText('Matter Description')).toBeInTheDocument()

      // Check that the summary section uses "Matter"
      expect(screen.getByText('Matter Summary')).toBeInTheDocument()

      // Verify litigation-specific fields are NOT shown
      expect(screen.queryByText('Litigation Details')).not.toBeInTheDocument()
    })

    it('should display "Matter" terminology for IP matters', () => {
      const ipMatter = createMockMatter(PracticeArea.IP)
      
      render(<MatterOverview matterData={ipMatter} />)

      // Check that the edit button uses "Matter"
      expect(screen.getByText('Edit Matter')).toBeInTheDocument()

      // Check that the description section uses "Matter"
      expect(screen.getByText('Matter Description')).toBeInTheDocument()

      // Check that the summary section uses "Matter"
      expect(screen.getByText('Matter Summary')).toBeInTheDocument()

      // Verify litigation-specific fields are NOT shown
      expect(screen.queryByText('Litigation Details')).not.toBeInTheDocument()
    })

    it('should handle matters without description gracefully', () => {
      const matterWithoutDescription = {
        ...createMockMatter(PracticeArea.LITIGATION),
        description: null
      }
      
      render(<MatterOverview matterData={matterWithoutDescription} />)

      // Should still show edit button with correct label
      expect(screen.getByText('Edit Case')).toBeInTheDocument()

      // Should not show description section
      expect(screen.queryByText('Case Description')).not.toBeInTheDocument()

      // Should still show summary section
      expect(screen.getByText('Case Summary')).toBeInTheDocument()
    })

    it('should call onEdit callback with correct matter ID when provided', () => {
      const mockOnEdit = vi.fn()
      const matter = createMockMatter(PracticeArea.LITIGATION)
      
      render(<MatterOverview matterData={matter} onEdit={mockOnEdit} />)

      const editButton = screen.getByText('Edit Case')
      editButton.click()

      expect(mockOnEdit).toHaveBeenCalledWith('matter-1')
      expect(mockPush).not.toHaveBeenCalled()
    })

    it('should navigate to edit page when no onEdit callback provided', () => {
      const matter = createMockMatter(PracticeArea.TRANSACTION)
      
      render(<MatterOverview matterData={matter} />)

      const editButton = screen.getByText('Edit Matter')
      editButton.click()

      expect(mockPush).toHaveBeenCalledWith('/matters/matter-1/edit')
    })
  })

  describe('Dynamic Label Logic Verification', () => {
    it('should correctly determine display labels for all practice areas', () => {
      const testCases = [
        { practiceArea: PracticeArea.LITIGATION, expectedLabel: 'Case' },
        { practiceArea: PracticeArea.TRANSACTION, expectedLabel: 'Matter' },
        { practiceArea: PracticeArea.ADVISORY, expectedLabel: 'Matter' },
        { practiceArea: PracticeArea.IP, expectedLabel: 'Matter' }
      ]

      testCases.forEach(({ practiceArea, expectedLabel }) => {
        const matter = createMockMatter(practiceArea)
        expect(matter.displayLabel).toBe(expectedLabel)
        expect(getMatterDisplayLabel(practiceArea)).toBe(expectedLabel)
      })
    })

    it('should correctly identify litigation vs non-litigation matters', () => {
      const litigationMatter = createMockMatter(PracticeArea.LITIGATION)
      const transactionMatter = createMockMatter(PracticeArea.TRANSACTION)
      const advisoryMatter = createMockMatter(PracticeArea.ADVISORY)
      const ipMatter = createMockMatter(PracticeArea.IP)

      expect(litigationMatter.practiceArea === PracticeArea.LITIGATION).toBe(true)
      expect(transactionMatter.practiceArea === PracticeArea.LITIGATION).toBe(false)
      expect(advisoryMatter.practiceArea === PracticeArea.LITIGATION).toBe(false)
      expect(ipMatter.practiceArea === PracticeArea.LITIGATION).toBe(false)
    })
  })

  describe('Conditional Content Display', () => {
    it('should show litigation-specific fields only for litigation matters', () => {
      const litigationMatter = createMockMatter(PracticeArea.LITIGATION)
      const transactionMatter = createMockMatter(PracticeArea.TRANSACTION)

      // Test litigation matter
      const { rerender } = render(<MatterOverview matterData={litigationMatter} />)
      
      expect(screen.getByText('Litigation Details')).toBeInTheDocument()
      expect(screen.getByText('Case Number')).toBeInTheDocument()
      expect(screen.getByText('Court')).toBeInTheDocument()

      // Test non-litigation matter
      rerender(<MatterOverview matterData={transactionMatter} />)
      
      expect(screen.queryByText('Litigation Details')).not.toBeInTheDocument()
      expect(screen.queryByText('Case Number')).not.toBeInTheDocument()
      expect(screen.queryByText('Court')).not.toBeInTheDocument()
    })

    it('should display matter statistics with correct terminology', () => {
      const litigationMatter = createMockMatter(PracticeArea.LITIGATION)
      const transactionMatter = createMockMatter(PracticeArea.TRANSACTION)

      // Test litigation matter
      const { rerender } = render(<MatterOverview matterData={litigationMatter} />)
      expect(screen.getByText('Case Summary')).toBeInTheDocument()

      // Test non-litigation matter
      rerender(<MatterOverview matterData={transactionMatter} />)
      expect(screen.getByText('Matter Summary')).toBeInTheDocument()
    })
  })
})
