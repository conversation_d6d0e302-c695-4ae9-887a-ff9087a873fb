/**
 * Integration tests for navigation component dynamic labeling
 * Tests that navigation components display correct terminology based on practice area
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { PracticeArea, MatterStatus, type Matter } from '@/types/domain/tenants/Matter'

// Mock the useMattersApi hook
const mockGetAllMatters = vi.fn()
vi.mock('@/hooks/useMattersApi', () => ({
  useMattersApi: () => ({
    getAllMatters: mockGetAllMatters,
    isLoading: false,
    error: null
  })
}))

// Mock Next.js router
vi.mock('next/navigation', () => ({
  usePathname: () => '/dashboard',
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
  })
}))

// Mock authentication
vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'user-1', email: '<EMAIL>' },
    isLoading: false,
    signOut: vi.fn()
  })
}))

// Mock subscription
vi.mock('@/hooks/useSubscription', () => ({
  useSubscription: () => ({
    subscription: { plan: 'pro', status: 'active' },
    isLoading: false
  })
}))

describe('Navigation Component Dynamic Labeling', () => {
  const createMockMatters = (practiceAreas: PracticeArea[]): Matter[] => {
    return practiceAreas.map((practiceArea, index) => ({
      id: `matter-${index + 1}`,
      tenantId: 'tenant-1',
      title: `Test ${practiceArea} ${index + 1}`,
      description: 'Test description',
      status: MatterStatus.ACTIVE,
      practiceArea,
      displayLabel: practiceArea === PracticeArea.LITIGATION ? 'Case' : 'Matter',
      sensitive: false,
      caseNumber: practiceArea === PracticeArea.LITIGATION ? `CV-2023-${index + 1}` : null,
      courtName: practiceArea === PracticeArea.LITIGATION ? 'Superior Court' : null,
      jurisdiction: practiceArea === PracticeArea.LITIGATION ? 'California' : null,
      filingDate: practiceArea === PracticeArea.LITIGATION ? '2023-01-15T00:00:00Z' : null,
      trialDate: practiceArea === PracticeArea.LITIGATION ? '2023-06-15T00:00:00Z' : null,
      primaryAttorneyId: 'attorney-1',
      priorityLevel: 'medium',
      statueOfLimitations: practiceArea === PracticeArea.LITIGATION ? '2025-01-15T00:00:00Z' : null,
      rejectionReason: null,
      createdBy: 'user-1',
      createdAt: '2023-01-01T00:00:00Z',
      updatedBy: null,
      updatedAt: null
    }))
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe('Sidebar Navigation', () => {
    // Import the Sidebar component dynamically to avoid module loading issues
    let Sidebar: any

    beforeEach(async () => {
      const module = await import('@/components/layout/sidebar')
      Sidebar = module.Sidebar
    })

    it('should display "Cases" in navigation when all matters are litigation', async () => {
      const litigationMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.LITIGATION
      ])
      
      mockGetAllMatters.mockResolvedValue(litigationMatters)

      render(
        <Sidebar 
          isOpen={true} 
          onToggle={() => {}} 
          onChatOpen={() => {}} 
        />
      )

      // Wait for the navigation label to be determined
      await waitFor(() => {
        expect(screen.getByText('Cases')).toBeInTheDocument()
      }, { timeout: 3000 })

      // Verify the quick action also uses "Case"
      await waitFor(() => {
        expect(screen.getByText('New Case')).toBeInTheDocument()
      })
    })

    it('should display "Matters" in navigation when practice areas are mixed', async () => {
      const mixedMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.TRANSACTION,
        PracticeArea.ADVISORY
      ])
      
      mockGetAllMatters.mockResolvedValue(mixedMatters)

      render(
        <Sidebar 
          isOpen={true} 
          onToggle={() => {}} 
          onChatOpen={() => {}} 
        />
      )

      // Wait for the navigation label to be determined
      await waitFor(() => {
        expect(screen.getByText('Matters')).toBeInTheDocument()
      }, { timeout: 3000 })

      // Verify the quick action also uses "Matter"
      await waitFor(() => {
        expect(screen.getByText('New Matter')).toBeInTheDocument()
      })
    })

    it('should default to "Cases" when no matters exist', async () => {
      mockGetAllMatters.mockResolvedValue([])

      render(
        <Sidebar 
          isOpen={true} 
          onToggle={() => {}} 
          onChatOpen={() => {}} 
        />
      )

      // Should default to "Cases" for new users
      await waitFor(() => {
        expect(screen.getByText('Cases')).toBeInTheDocument()
      })

      await waitFor(() => {
        expect(screen.getByText('New Case')).toBeInTheDocument()
      })
    })

    it('should fallback to "Cases" on API error', async () => {
      mockGetAllMatters.mockRejectedValue(new Error('API Error'))

      render(
        <Sidebar 
          isOpen={true} 
          onToggle={() => {}} 
          onChatOpen={() => {}} 
        />
      )

      // Should fallback to "Cases" on error
      await waitFor(() => {
        expect(screen.getByText('Cases')).toBeInTheDocument()
      })
    })
  })

  describe('Navigation Label Logic Verification', () => {
    it('should correctly identify all-litigation scenarios', () => {
      const allLitigationMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.LITIGATION,
        PracticeArea.LITIGATION
      ])

      const allLitigation = allLitigationMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)
      expect(allLitigation).toBe(true)
    })

    it('should correctly identify mixed practice area scenarios', () => {
      const mixedMatters = createMockMatters([
        PracticeArea.LITIGATION,
        PracticeArea.TRANSACTION
      ])

      const allLitigation = mixedMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)
      expect(allLitigation).toBe(false)
    })

    it('should correctly identify non-litigation scenarios', () => {
      const nonLitigationMatters = createMockMatters([
        PracticeArea.TRANSACTION,
        PracticeArea.ADVISORY,
        PracticeArea.IP
      ])

      const allLitigation = nonLitigationMatters.every(matter => matter.practiceArea === PracticeArea.LITIGATION)
      expect(allLitigation).toBe(false)
    })
  })
})
